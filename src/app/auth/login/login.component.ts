import { Component, ChangeDetectorRef, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { catchError, concatMap, map, switchMap, takeUntil } from 'rxjs/operators';
import { Observable, BehaviorSubject, Subject, forkJoin, zip, throwError } from 'rxjs';
import { MessageService } from 'primeng/api';
import { ConfigService } from 'src/app/services/config.service';
import { AuthenticationService } from 'src/app/services/authentication.service';
import { PopupService } from 'src/app/services/popup.service';
import { SettingsService } from 'src/app/services/settings.service';
import { LayoutService } from 'src/app/layout/service/app.layout.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})

export class LoginComponent implements OnInit {
  forgetPasswordLoading: boolean = false;
  showPassword = false;
  showSSOlogin: boolean;
  showADSdomain: boolean;
  ADSdomains = [];
  DomainPlaceholder: string = 'Domain';
  EmailPlaceholder: string = 'Email';
  loginInfo: any;
  getLocalDomain: string;
  getURLDomain: string;
  getConfigDomain: string;
  setDomain: string;
  localEmail: string;
  localName: string;
  errmsg: string;
  loginForm: FormGroup;
  languages = [
    {
      key:'en',
      display: 'English'
    },
    {
      key:'fr',
      display: 'Français'
    },
    {
      key:'es',
      display: 'Español'
    }
  ];
  currentLanguage: any;
  loggedIn = new BehaviorSubject<boolean>(false);
  selectedTheme: string;
  isSignUp: boolean = false;
  returnURL: string;
  isLanguage: boolean = false;
  defaultPage: string;
  sidebar: string;
  private ngUnsubscribe = new Subject<void>();

  // New properties for login mode toggle
  isSSOMode: boolean = false;
  showSSOToggle: boolean = false;

  constructor(private appConfigService: ConfigService,
    private cdr: ChangeDetectorRef,
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private authservice: AuthenticationService,
    private popupservice: PopupService,
    private breakpointObserver: BreakpointObserver,
    private settingService: SettingsService,
    private messageService: MessageService,
    public layoutService: LayoutService) 
  {
    this.currentLanguage = localStorage.getItem('language');
    this.isLanguage = this.currentLanguage ? true : false;
    this.selectedTheme = localStorage.getItem('theme');
  }

  ngOnInit() {
    this.returnURL = this.route.snapshot.queryParams['returnUrl'];
    this.localEmail = localStorage.getItem('email');
    this.localName = localStorage.getItem('name');
    this.loginForm = this.fb.group({
      domain: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required]
    });
    this.route.queryParams.subscribe(params => {
      if (!params['error']) {
        localStorage.removeItem('token');
        this.loggedIn.next(false);
        this.router.navigate(['login']);
      } else {
        this.errmsg = params['error'];
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  ngAfterViewInit() {
    var getURL = window.location.href;
    var URLobj = new URL(getURL);
    this.getURLDomain = URLobj.searchParams.get("domain");  //get domain from URL
    this.getLocalDomain = localStorage.getItem('domain');  //get domain from localstorage
    this.getConfigDomain = this.appConfigService.domain;  //get domain from config file

    this.getDomain();
    this.loginForm.patchValue({
      domain: this.setDomain,
      email: this.localEmail
    });
    this.loginInfo = this.appConfigService.login;
    if(this.loginInfo) {
      this.checkLoginType();

      // Auto-toggle to SSO mode if login_type is set to SSO in localStorage
      const savedLoginType = localStorage.getItem('login_type');
      if (savedLoginType === 'SSO' && this.showSSOToggle && !this.getLocalDomain) {
        this.toggleToSSOMode();
      }
    }
    this.cdr.detectChanges();
  }

  getDomain() {
    if (this.getLocalDomain?.length > 0) { // check domain is there or not
      this.setDomain = this.getLocalDomain;
    } else if (this.getURLDomain?.length > 0) {
      this.setDomain = this.getURLDomain;
    } else if (this.getConfigDomain?.length > 0) {
      this.setDomain = this.getConfigDomain;
    } else {
      this.setDomain = ' ';
    }
    setTimeout(() => {
      this.loginForm.get('domain').patchValue(this.setDomain);
      this.loginForm.get('domain').updateValueAndValidity();
    }, 300);
  }

  checkLoginType() {
    if (this.loginInfo.type.includes('saml')) {
      this.showSSOlogin = true;
      this.showSSOToggle = true; // Show SSO toggle option
    } else if (this.loginInfo.type.includes('ads')) {
      this.showSSOlogin = false;
      this.showSSOToggle = false;
      this.loginForm.get('email').setValidators([Validators.required]);
      this.loginForm.get('email').updateValueAndValidity();
      this.DomainPlaceholder = 'Company';
      this.EmailPlaceholder = 'Active Directory User Name';
      if (this.loginInfo.adsdomain.length === 1) {
          this.showADSdomain = false;
          this.loginForm.addControl('adsdomainname', new FormControl(this.loginInfo.adsdomain[0], Validators.required));
      } else {
        this.showADSdomain = true;
        this.loginForm.addControl('adsdomainname', new FormControl('', Validators.required));
        this.ADSdomains = this.loginInfo.adsdomain;
      }
    } else if (this.loginInfo.type.includes('sap')) {
      this.showSSOlogin = false;
      this.showSSOToggle = false;
      this.loginForm.get('email').setValidators([Validators.required]);
      this.loginForm.get('email').updateValueAndValidity();
      this.EmailPlaceholder = 'SAP User Name';
    } else if (this.loginInfo.type.includes('email')) {
      this.showSSOlogin = false;
      this.showSSOToggle = false;
      this.EmailPlaceholder = 'Email';
    }
  }

  toggleToSSOMode() {
    this.isSSOMode = true;
    // Save login type preference
    localStorage.setItem('login_type', 'SSO');
    // In SSO mode, only domain is required
    this.loginForm.get('email').clearValidators();
    this.loginForm.get('password').clearValidators();
    this.loginForm.get('email').updateValueAndValidity();
    this.loginForm.get('password').updateValueAndValidity();
  }

  toggleToEmailMode() {
    this.isSSOMode = false;
    // Save login type preference
    localStorage.setItem('login_type', 'EMAIL');
    // In Email mode, all fields are required - but respect login type for email validation
    if (this.loginInfo?.type.includes('ads') || this.loginInfo?.type.includes('sap')) {
      // For ADS/SAP, email field is username - no email validation needed
      this.loginForm.get('email').setValidators([Validators.required]);
    } else {
      // For regular email login, use email validation
      this.loginForm.get('email').setValidators([Validators.required, Validators.email]);
    }
    this.loginForm.get('password').setValidators([Validators.required]);
    this.loginForm.get('email').updateValueAndValidity();
    this.loginForm.get('password').updateValueAndValidity();
  }

  useDifferentAcc() {
    localStorage.clear();
    this.getLocalDomain = localStorage.getItem('domain');
    this.localEmail = localStorage.getItem('email');
    this.localName = localStorage.getItem('name');
    this.currentLanguage = localStorage.getItem('language');
    this.isLanguage = this.currentLanguage ? true : false;
    this.loginForm.reset();
    this.getDomain();
    this.router.navigate(['login']);
  }

  forgetPassword() {
    this.forgetPasswordLoading = true;
    if (this.loginForm.get('domain').hasError('required') && this.loginForm.get('email').hasError('required')) {
      this.forgetPasswordLoading = false;
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Doamin and Email are required to reset password.' });
    } else if (!this.loginForm.get('domain').hasError('required') && this.loginForm.get('email').hasError('required')) {
      this.forgetPasswordLoading = false;
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Email is required to reset password.' });
    } else if (!this.loginForm.get('email').hasError('required') && this.loginForm.get('domain').hasError('required')) {
      this.forgetPasswordLoading = false;
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Domain is required to reset password.' });
    } else {
      this.authservice.forgotpassword(this.loginForm.value)
        .subscribe({
          next:(res: any) => {
            if (res.status === 204) {
              this.forgetPasswordLoading = false;
              this.messageService.add({ severity:'success', summary:'Success', detail: 'Please check your mailbox.' });
              localStorage.removeItem('language');
              this.currentLanguage = localStorage.getItem('language');
              this.isLanguage = this.currentLanguage ? true : false;
            } else {
              this.forgetPasswordLoading = false;
              this.messageService.add({ severity:'error', summary:'Error', detail: 'Email is not registered.' });
            }
          },
          error: (error) => {
            this.forgetPasswordLoading = false;
            this.errmsg = error.error.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
      });
    }
  }

  goToSignUpPage() {
    this.isSignUp = true;
  }

  goToLoginPage() {
    this.isSignUp = false;
  }

  signUpRequest() {
    if (this.loginForm.get('domain').hasError('required') && this.loginForm.get('email').hasError('required')) {
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Please enter doamin and email to sign up.' });
    } else if (!this.loginForm.get('domain').hasError('required') && this.loginForm.get('email').hasError('required')) {
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Email is required for sign up.' });
    } else if (!this.loginForm.get('email').hasError('required') && this.loginForm.get('domain').hasError('required')) {
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Domain is required for sign up.' });
    } else {
      this.authservice.signup(this.loginForm.value).subscribe({
        next: (res: any) => {
            if (res.status === 204) {
              this.messageService.add({ severity:'info', summary:'Info', detail: 'The signup link has been sent to your email, please check your mailbox.' });
              this.isSignUp = false;
            } else {
              this.messageService.add({ severity:'error', summary:'Error', detail: 'Error signing up, please try after some time.' });
            }
          },
          error: (error) => {
            this.errmsg = error.error.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
      });
    }
  }

  login() {
    // Check validation based on current mode
    const isValidForCurrentMode = this.isSSOMode ?
      (this.loginForm.get('domain').valid && this.loginForm.get('domain').value?.trim()) :
      this.loginForm.valid;

    if (isValidForCurrentMode) {
      if (this.isSSOMode) {
        // Call SSO login method with domain parameter
        this.openPopup();
        return;
      }

      // Email login flow
      this.selectLanguage({value: 'en'})
      this.authservice.authenticateUser(this.loginForm.value).pipe(
        switchMap((loginResponse: any) => {
          if (loginResponse.error === "") {
            if (this.currentLanguage) {
              localStorage.setItem('language', this.currentLanguage);
            } else {
              this.currentLanguage = 'en';
              localStorage.setItem('language', this.currentLanguage);
            }
            return this.layoutService.getProfileSetting(); // Call first API after login
          } else {
            this.messageService.add({ severity:'error', summary:'Error', detail: loginResponse.error });
            return throwError(() => null);
          }
        }),
        catchError(error => {
            this.errmsg = error.error.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
            return throwError(() => error); // Throw an error to stop the observable chain
        }),
        switchMap(() => this.layoutService.getProfileData())).pipe(takeUntil(this.ngUnsubscribe)).subscribe((res: any)=>{ 
          if (res.status?.toLowerCase() === 'success') {
            let completedCount = 0;
            completedCount += 1;
            // default page
            if (this.returnURL) {
              this.router.navigateByUrl(this.returnURL);
            } else if (res?.profileSettings?.defaultPage) {
              const defaultPage = res?.profileSettings?.defaultPage;
              this.router.navigateByUrl(defaultPage);
            } else {
              this.router.navigateByUrl('/home');
            }
            // Sidebar
            if (res?.profileSettings?.sidebar) {
              this.sidebar = res.profileSettings.sidebar;
              if (this.sidebar === 'Collapsed') {
                this.layoutService.onButtonHover();
              } else {
                this.layoutService.onClickSidebar();
              }
            } else {
              this.sidebar = "Expanded";
            }
            // this.layoutService.sidebarChange.next(this.sidebar);
            // if (completedCount > 1) {
              this.unsubscribeObservable();
            // }
          } else {
            this.router.navigateByUrl('/home');
            this.sidebar = "Expanded";
          }
      });
    } else {
      // Show validation errors based on current mode
      if (this.isSSOMode) {
        if (!this.loginForm.get('domain').value?.trim()) {
          // Mark domain field as touched to show visual validation feedback
          this.loginForm.get('domain').markAsTouched();
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Domain is required for SSO login'
          });
          return;
        }
      } else {
        // For email mode, mark all fields as touched to show validation errors
        Object.keys(this.loginForm.controls).forEach(key => {
          this.loginForm.get(key).markAsTouched();
        });
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Please fill in all required fields'
        });
        return;
      }
    }
  }

  // Check if login button should be enabled
  get isLoginButtonEnabled(): boolean {
    if (this.isSSOMode) {
      return this.loginForm.get('domain').valid && this.loginForm.get('domain').value?.trim();
    } else {
      return this.loginForm.valid;
    }
  }

      // this.authservice.authenticateUser(this.loginForm.value).subscribe({
      //   next: (response: any) => {
      //     if (response.error === "") {
      //       zip([
      //         this.layoutService.getProfileSetting(),
      //         this.layoutService.getProfileData()
      //       ]).subscribe(([profileResponse, profileData]) => {
      //         console.log('ress',profileData)
      //         // default page
      //         if (this.returnURL) {
      //           console.log('this.returnURL',this.returnURL);
      //           this.router.navigateByUrl(this.returnURL);
      //         } else if (profileData?.profileSettings?.defaultPage) {
      //           const defaultPage = profileData?.profileSettings?.defaultPage;
      //           console.log('this.defaultPage',defaultPage);

      //           this.router.navigateByUrl(defaultPage);
      //         } else {
      //           console.log('home');

      //           this.router.navigateByUrl('/home');
      //         }
      //       // Sidebar
      //         if (profileData?.profileSettings?.sidebar) {
      //           this.sidebar = profileData.profileSettings.sidebar;
      //           if (this.sidebar === 'Collapsed') {
      //             this.layoutService.onButtonHover();
      //           } else {
      //             this.layoutService.onClickSidebar();
      //           }
      //         } else {
      //           this.sidebar = "Expanded";
      //         }
      //         this.layoutService.sidebarChange.next(this.sidebar);
      //       });

      //       this.layoutService.getProfileSetting();
      //       this.layoutService.getProfileData().pipe(
      //         takeUntil(this.ngUnsubscribe)
      //         ).subscribe((res: any)=>{ 
      //         console.log('profile data login',res);
      //       // default page
      //         if (this.returnURL) {
      //           console.log('this.returnURL',this.returnURL);
      //           this.router.navigateByUrl(this.returnURL);
      //         } else if (res?.profileSettings?.defaultPage) {
      //           const defaultPage = res?.profileSettings?.defaultPage;
      //           console.log('this.defaultPage',defaultPage);

      //           this.router.navigateByUrl(defaultPage);
      //         } else {
      //           console.log('home');

      //           this.router.navigateByUrl('/home');
      //         }
      //       // Sidebar
      //         if (res?.profileSettings?.sidebar) {
      //           this.sidebar = res.profileSettings.sidebar;
      //           if (this.sidebar === 'Collapsed') {
      //             this.layoutService.onButtonHover();
      //           } else {
      //             this.layoutService.onClickSidebar();
      //           }
      //         } else {
      //           this.sidebar = "Expanded";
      //         }
      //         this.layoutService.sidebarChange.next(this.sidebar);
      //       // Theme
      //         // if (res.profileSettings?.theme) {
      //         //   this.layoutService.changeTheme(res.profileSettings?.theme.theme, res.profileSettings?.theme.colorScheme);
      //         // } else {
      //         //   this.layoutService.changeTheme('lara-light-indigo', 'light');
      //         // }
      //       // Form designer
      //         // if (res.profileSettings?.formDesigner) {
      //         //   const formDesignerOption = res.profileSettings?.formDesigner;
      //         //   localStorage.setItem('Designer', formDesignerOption);
      //         // }
      //       });
      //       this.settingService.getPersonalization().subscribe(response => {
      //         if (response.status.toLowerCase() === 'success') {
      //           if (response?.profileSettings) {
      //             if (response.profileSettings?.sidebar) {
      //               const sidebarOption = response.profileSettings?.sidebar;
      //               localStorage.setItem('sidebar', sidebarOption);
      //               console.log('login called')
      //               if (sidebarOption === 'Collapsed') {
      //                 this.layoutService.onButtonHover();
      //                 console.log('Collapsed')
      //               } else {
      //                 this.layoutService.onClickSidebar();
      //                 console.log('expaneded')
      //               }
      //               this.layoutService.sidebarChange.next(sidebarOption)
      //             }
      //             if (response.profileSettings?.formDesigner) {
      //               const formDesignerOption = response.profileSettings?.formDesigner;
      //               localStorage.setItem('Designer', formDesignerOption);
      //             }
      //             if (response.profileSettings?.defaultPage) {
      //               this.defaultPage = response.profileSettings?.defaultPage; 
      //               this.router.navigateByUrl(this.returnURL || this.defaultPage || '/home');
      //             }
      //             if (response.profileSettings?.theme) {
      //               this.layoutService.changeTheme(response.profileSettings?.theme.theme, response.profileSettings?.theme.colorScheme);
      //             } else {
      //               this.layoutService.changeTheme('lara-light-indigo', 'light');
      //             }
      //           } else {
      //             this.router.navigateByUrl(this.returnURL || '/home');
      //           }
      //         } else {
      //           this.router.navigateByUrl(this.returnURL || '/home');
      //         }
      //       });
            
      //     } else {
      //       this.messageService.add({ severity:'error', summary:'Error', detail: response.error });
      //     }
      //   },
      //   error: (error) => {
      //     this.errmsg = error.error.error;
      //     this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      //   }
      // });

  openPopup() {
    const domainValue = this.loginForm.get('domain').value?.trim();
    const popupPromise = this.popupservice.openPopup('login', domainValue);

    if (popupPromise) {
      popupPromise.then((result: any) => {
        if (!result.success) {
          // Get technical details from popup service
          const loginUrl = this.popupservice.getLoginUrl('login', domainValue);
          const redirectUrl = this.popupservice.getRedirectUrlPath();

          // Format error message with actual technical details
          const errorDetail = result.error +
            '\n\nTechnical Info:' +
            '\nDomain: ' + (domainValue || 'Not specified') +
            '\nLogin URL: ' + loginUrl +
            '\nRedirect URL: ' + redirectUrl;

          this.messageService.add({
            severity: 'error',
            summary: 'SSO Login Error',
            detail: errorDetail
          });
        }
      }).catch((error: any) => {
        // Get technical details for connection errors
        const loginUrl = this.popupservice.getLoginUrl('login', domainValue);
        const redirectUrl = this.popupservice.getRedirectUrlPath();

        // Format error message with actual technical details
        const baseError = error.message || 'SSO login failed';
        const errorDetail = baseError +
          '\n\nTechnical Info:' +
          '\nDomain: ' + (domainValue || 'Not specified') +
          '\nLogin URL: ' + loginUrl +
          '\nRedirect URL: ' + redirectUrl;

        this.messageService.add({
          severity: 'error',
          summary: 'SSO Login Error',
          detail: errorDetail
        });
      });
    }
  }

  selectLanguage(event: any) {
    this.currentLanguage = event?.value;
    if (this.currentLanguage) {
      localStorage.setItem('language', this.currentLanguage);
      this.currentLanguage = localStorage.getItem('language');
    } else {
      this.currentLanguage = 'en';
      localStorage.setItem('language', this.currentLanguage);
    }
  }

  isDarkTheme(): boolean {
    if (this.selectedTheme?.replace('-', ' ').includes("dark") || this.selectedTheme === 'arya-green' || this.selectedTheme === 'vela-purple') {
      return true;
    } else {
      return false;
    }
  }

  unsubscribeObservable() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

}
