.required:after {
    content: " *";
    color: red;
  }
  .field {
    margin-bottom: 8px !important;
  }
  .float-left {
    float: left;
  }
  .float-right {
    float: right;
  }
  
  /* .custom-button:hover {
    background-color: #2586c7;
  } */
  .custom-error {
    position: absolute;
    top: 0;
  }
 
  .p-button {
    text-decoration: none !important;
  }
  

  .bg-color {
    background-color: var(--surface-ground);
    display: flex;
    height: 100vh;
    justify-content: center;
    align-items: center;
  }
  .main-container {
    background-color: #fff;
    border-radius: 50px;
    box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
    position: relative;
    width: 600px;
    max-width: 100%;
    min-height: 650px;
  }
  .center-container {
    padding: 2px;
  }
  .custom-button {
    min-width: 140px;
    margin-top: 20px;
  }
  .child-container {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translateY(-50%) translateX(-50%);
  }
  .logo {
    position: absolute;
    top: 10px;
    left: 38%;
    max-width: 140px;
  }
  .forgot-password-link {
    color: #1D4ED8;
    cursor: pointer;
    float: right;
    margin-top: 2px;
  }
  .extra-links {
    color: #1D4ED8;
    cursor: pointer;
  }

  .language-dropdown-container {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
  }

  .language-dropdown {
    width: 150px;
  }

  .language-dropdown-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .language-icon {
    color: #6B7280;
    font-size: 16px;
  }

  .button-link {
    color: var(--primary-color);
    cursor: pointer;
    border: 1px solid var(--primary-color);
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
    min-width: 80px;
  }

  .button-link:hover {
    background-color: var(--primary-color);
    color: var(--primary-color-text);
  }