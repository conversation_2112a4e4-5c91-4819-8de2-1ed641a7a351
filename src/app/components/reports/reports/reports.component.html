<p-toast></p-toast>
<p-confirmPopup></p-confirmPopup>
<div class="card mb-0">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
            <!-- <a href="https://docs.unvired.com/builder/advanced/reports/" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
            <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        </ng-template>
    </p-toolbar>

<!-- Accordian -->
    <div class="grid">
        <div class="col-12">
            <p-accordion styleClass="mb-3">
                <p-accordionTab [selected]="true">
                    <ng-template pTemplate="header">
                        <div class="flex flex-row justify-content-between w-full">
                            <div class="flex flex-row gap-2 align-items-center">
                                <span style="width: 18rem">
                                    <p-dropdown id="Status" [(ngModel)]="reportId" optionLabel="reportName" optionValue="reportId" (onChange)="selectedReport(reportId)"
                                        [options]="reportArr" appendTo="body" (click)="$event.stopPropagation()" placeholder="Select Report" styleClass="w-full" [autoDisplayFirst]="false">
                                    </p-dropdown>
                                </span>
                                <span *ngIf="reportDescription" class="form-subtext">Description: {{reportDescription}}</span>
                                <span *ngIf="formName?.length > 0" class="form-subtext"> | Form: {{formName[0].formName}}</span>
                            </div>
                            <div class="flex flex-row align-items-center">
                                <button class="ml-2" pButton type="button" icon="pi pi-plus" (click)="showCreateReport();$event.stopPropagation()" pTooltip="Create Report"></button>
                                <button *ngIf="reportDetails?.reportName !== 'Default'" class="ml-2" pButton type="button" icon="pi pi-pencil" (click)="showUpdateReport();$event.stopPropagation()" pTooltip="Update Report"></button>
                                <button *ngIf="reportDetails?.reportName !== 'Default'" class="p-button-danger ml-2" pButton type="button" icon="pi pi-trash" (click)="deleteReport($event)" pTooltip="Delete Report"></button>
                                <!-- <button class="ml-2" pButton type="button" icon="pi pi-caret-right" label="Run" (click)="showRunReport();$event.stopPropagation()" pTooltip="Run Report"></button>         -->
                            </div>
                        </div>
                    </ng-template>
                    
                    <ng-template pTemplate="content">
                        <!-- <div class="grid">
                            <div class="col-4 mt-3">
                                <span class="p-float-label">
                                    <input pInputText id='Name' [(ngModel)]="reportName" class="w-full" type="text" required="true" [disabled]="true">
                                    <label htmlFor="Name">Report Name</label>
                                </span>
                            </div>
                            <div class="col-4 mt-3">
                                <div class="p-float-label">
                                    <textarea id="Description" pInputTextarea [(ngModel)]="reportDescription" rows="1" cols="30" class="w-full" [disabled]="true"></textarea>
                                    <label htmlFor="Description">Report Description</label>
                                </div>
                            </div>
                            <div *ngIf="selectedForm" class="col-4 mt-3">
                                <span class="p-float-label">
                                    <p-dropdown id="Form" [(ngModel)]="selectedForm" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                        (onChange)="onFormSelect($event)" optionLabel="formTitle" optionValue="formId" [options]="formList" appendTo="body" [disabled]="true">
                                    </p-dropdown>
                                    <label htmlFor="Form">Select Form</label>
                                </span>
                            </div>
                        </div> -->

        <!-- Run report form -->
                        <!-- <p-divider *ngIf="isRunReport"></p-divider> -->
                        <div *ngIf="isRunReport" class="grid formgrid">
                            <div class="col-4 mt-4">
                                <span class="p-float-label">
                                    <p-calendar id="start" [(ngModel)]="filStartDate" [maxDate]="maxDate" [readonlyInput]="true" styleClass="w-full" appendTo="body"></p-calendar>
                                    <label for="start" class="mb-0 _required">Start Date</label>
                                </span>
                            </div>
                            <div class="col-4 mt-4">
                                <span class="p-float-label">
                                    <p-calendar id="end" [(ngModel)]="filEndDate" [maxDate]="maxDate" [readonlyInput]="true" styleClass="w-full" appendTo="body"></p-calendar>
                                    <label for="end" class="mb-0 _required">End Date</label>
                                </span>
                            </div>
                            <div *ngIf="reportDetails.filterFields && reportDetails.filterFields.includes('Status')" class="col-4 mt-4">
                                <span class="p-float-label">
                                    <p-dropdown id="Status" [(ngModel)]="filStatus" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                        [options]="tasks" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Status" >Select Status</label>
                                </span>
                            </div>
                            
                            <!-- Delayed Submission -->
                            <div *ngIf="reportDetails.filterFields && reportDetails.filterFields.includes('Delayed Submission Only')" class="col-4 mt-4">
                                <div class="flex align-items-center">
                                    <p-checkbox value="Delayed Submission Only" [(ngModel)]="filDelayedSub" inputId="Delayed" [binary]="true"></p-checkbox>
                                    <label for="Delayed" class="mb-0 ml-2">Delayed Submission Only</label>
                                </div>
                            </div>

                            <!-- Team -->
                            <div *ngIf="reportDetails.filterFields && reportDetails.filterFields.includes('Team')" class="col-4 mt-4">
                                <span class="p-float-label">
                                    <p-dropdown id="Team" [(ngModel)]="filTeam" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                        optionLabel="teamName" optionValue="teamId" [options]="teamsArr" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Team">Select Team</label>
                                </span>
                            </div>

                            <!-- User -->
                            <div *ngIf="reportDetails.filterFields && reportDetails.filterFields.includes('User')" class="col-4 mt-4">
                                <span class="p-float-label">
                                    <p-dropdown id="Team" [(ngModel)]="filUser" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                        optionLabel="firstName" optionValue="id" [options]="usersArr" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Team">Select User</label>
                                </span>
                            </div>

                            <!-- Category -->
                            <div *ngIf="reportDetails.filterFields && reportDetails.filterFields.includes('Category')" class="col-4 mt-4">
                                <span class="p-float-label">
                                    <p-dropdown id="User" [(ngModel)]="filCategory" [filter]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                        optionLabel="category" optionValue="category" [options]="formCategories" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="User">Select Category</label>
                                </span>
                            </div>

                            <div *ngIf="reportDetails?.formId && filFormQuery" class="col-12 mt-4">
                                <div class="card">
                                    <div style="text-align: left" class="query-builder" *ngIf="(config.fields | json) != '{}'; else nofields">
                                        <query-builder [(ngModel)]="filFormQuery" [ngModelOptions]="{ standalone: true }" [config]="config" [allowRuleset]="allowRuleset"
                                        [allowCollapse]="allowCollapse" [persistValueOnFieldChange]="persistValueOnFieldChange">
                                            <ng-container *queryButtonGroup="let ruleset;let addRule = addRule;let addRuleSet = addRuleSet;let removeRuleSet = removeRuleSet ;let index = index">
                                                <p-button [disabled]="isDBdisabled" icon="pi pi-plus" styleClass="p-button-info" class="mr-3" (onClick)="addRule();addRulesett(ruleset)"></p-button>
                                                <p-button [disabled]="isDBdisabled" icon="pi pi-plus-circle" styleClass="p-button-info" class="mr-3" *ngIf="addRuleSet" (onClick)="addRuleSet()"></p-button>
                                                <p-button [disabled]="isDBdisabled" icon="pi pi-minus-circle" styleClass="p-button-danger" class="mr-3 mt-1" *ngIf="removeRuleSet" (onClick)="removeRuleSet()"></p-button>
                                            </ng-container>
                                            <ng-container *queryArrowIcon>
                                                <i class="pi pi-angle-right" ngClass="mat-arrow-icon"></i>
                                            </ng-container>
                                        <!--AND / OR STARTS-->
                                            <ng-container *querySwitchGroup="let ruleset; let onChange = onChange" >
                                                <div style=" margin: 5px 0px 0px 5px !important;">
                                                    <p-selectButton [disabled]="isDBdisabled" [options]="stateOptions" class="selectBtn" [(ngModel)]="ruleset.condition" [ngModelOptions]="{ standalone: true }"
                                                    (ngModelChange)="onChange($event)" optionLabel="label" optionValue="value"></p-selectButton>
                                                </div>
                                            </ng-container>
                                        <!--AND / OR ENDS-->
                                        <div class="grid" >
                                            <div class="col-1 reducePadding" *queryRemoveButton="let rule; let removeRule = removeRule">
                                                <p-button [disabled]="isDBdisabled" icon="pi pi-trash" styleClass="delQueryBtn p-button-danger" (onClick)="removeRule(rule)"></p-button>
                                            </div>
                                            <div class="col-4 reducePadding" *queryEntity=" let rule; let entities = entities; let onChange = onChange">
                                                <p-dropdown [disabled]="isDBdisabled" [options]="entities" [(ngModel)]="rule.entity" optionLabel="name" optionValue="value"
                                                    [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange($event, rule)" [autoDisplayFirst]="true" appendTo="body"></p-dropdown>
                                            </div> 
                                            <!--SELECT FILEDS STARTS--> 
                                            <div class="col-4 reducePadding" *queryField="let rule;let fields = fields;let onChange = onChange;let getFields = getFields">
                                                <p-dropdown [disabled]="isDBdisabled" [options]="getFields(rule.entity)" [(ngModel)]="rule.field" optionLabel="name" optionValue="value"
                                                    [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange($event, rule)" [autoDisplayFirst]="true" (onChange)="getData(rule)"
                                                    appendTo="body" class="formsNodeOp"></p-dropdown>
                                            </div>
                                        <!--SELECT FILEDS ENDS-->
                                        <!--SELECT Operation STARTS-->
                                        <div class="col-4 reducePadding" *queryOperator="let rule;let operators = operators;let onChange = onChange">
                                            <p-dropdown [disabled]="isDBdisabled" [options]="operators" [(ngModel)]="rule.operator" 
                                                [ngModelOptions]="{ standalone: true }" (ngModelChange)="ruleOperationChanged(rule)" (ngModelChange)="onChange(rule)" 
                                                [autoDisplayFirst]="true" appendTo="body" class="formsNodeOp"></p-dropdown>
                                        </div>
                                        <!--SELECT operation ENDS-->
                                        <!--Select for radio or checkbox or dropdown STARTS-->
                                        <ng-container *queryInput=" let rule; let field = field; let options = options; type: 'category'; let onChange = onChange ">
                                            <div class="col-1 py-3">
                                                <div class="flex flex-row align-items-center">
                                                    <span *ngIf="rule.checked == false" class="mb-1">Default</span>
                                                    <span *ngIf="rule.checked == true" class="mb-1">Custom</span>
                                                    <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                                                        (ngModelChange)="ruleSwitchChangedDropdown(rule)" (ngModelChange)="onChange(rule)" class="ml-2"></p-inputSwitch>    
                                                </div>
                                            </div>
                                            <div class="col-2 reducePadding" *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null'">
                                                <div *ngIf="!rule.checked">
                                                    <p-dropdown [options]="options"  (ngModelChange)="onChange(rule)" [(ngModel)]="rule.value"
                                                        optionLabel="name" optionValue="value" [ngModelOptions]="{ standalone: true }"
                                                        (ngModelChange)="onChange()" [autoDisplayFirst]="true" appendTo="body" styleClass="w-full"></p-dropdown>
                                                </div>
                                                <div *ngIf="rule.checked">
                                                    <span class="p-input-icon-right">
                                                        <input pInputText [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                                    <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                                    </span>
                                                </div>
                                            </div>
                                        </ng-container>
                                        <!--Select for radio or checkbox ENDS-->
                                        <!-- type is date STRAT -->
                                                    <ng-container *queryInput="let rule; let field = field; type: 'date'; let onChange = onChange;let checked = checked;">
                                                        <div class="col-1 py-3">
                                                            <div class="flex flex-row align-items-center">
                                                                <span *ngIf="rule.checked == false" class="mb-1">Default</span>
                                                                <span *ngIf="rule.checked == true" class="mb-1">Custom</span>
                                                                <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                                                                    (ngModelChange)="ruleSwitchChanged(rule)" (ngModelChange)="onChange(rule)" class="ml-2"></p-inputSwitch>    
                                                            </div>
                                                        </div>
                                                        <ng-container *ngIf="rule.operator === 'between' || rule.operator === 'not between'">
                                                            <div class="col-2 reducePadding" *ngIf="!rule.checked">
                                                                <p-calendar selectionMode="range" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" showClear="true"
                                                                    (ngModelChange)="onChange()" appendTo="body" [iconDisplay]="'input'" [showIcon]="true" styleClass="w-full"></p-calendar>
                                                            </div>
                                                            <div class="col-2 reducePadding" *ngIf="rule.checked">     
                                                                <span class="p-input-icon-right">
                                                                    <input pInputText style="width: 160px;" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                                                    <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                                                </span>
                                                            </div>
                                                        </ng-container>
                                                    
                                                        <div class="col-2 reducePadding" *ngIf=" rule.operator !== 'between' && rule.operator !== 'not between' && rule.operator !== 'is null' && rule.operator !== 'is not null' && !rule.checked">
                                                            <p-calendar [(ngModel)]="rule.value" showClear="true" [ngModelOptions]="{ standalone: true }"
                                                                (ngModelChange)="onChange()" appendTo="body" styleClass="w-full" [showTime]="true"></p-calendar>
                                                        </div>
                                                        <div class="col-2 reducePadding" *ngIf=" rule.operator !== 'between' && rule.operator !== 'not between' && rule.operator !== 'is null' && rule.operator !== 'is not null' && rule.checked">
                                                            <span class="p-input-icon-right">
                                                                <input pInputText style="width: 100%"  [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                                                <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)" ></i> -->
                                                            </span>
                                                        </div>
                                                    </ng-container>
                                        <!-- type = date ENDS -->
                                        <!-- type = number -->
                                                        <ng-container *queryInput=" let rule; let field = field; type: 'number'; let onChange = onChange">
                                                            <ng-container *ngIf="rule.operator === 'between' || rule.operator === 'not between'">
                                                                <div class="col-1 reducePadding">
                                                                    <span class="p-input-icon-right">
                                                                        <input pInputText  style="width: 100%" [(ngModel)]="rule.value[0]" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                                                        <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value[0],1)"></i> -->
                                                                    </span>
                                                                </div>
                                                                <div style="margin-top:20px !important;">To</div>
                                                                <div class="col-1 reducePadding">
                                                                    <span class="p-input-icon-right">
                                                                        <input pInputText  style="width: 100%;" [(ngModel)]="rule.value[1]" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                                                        <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value[1],2)"></i> -->
                                                                    </span>
                                                                </div>
                                                            </ng-container>
                                                            <ng-container *ngIf="rule.operator !== 'between' && rule.operator !== 'not between'">
                                                                <div class="col-3 reducePadding">
                                                                    <span class="p-input-icon-right">
                                                                        <input style="width: 100%" type="text" pInputText styleClass="w-full" class="w-full" placeholder="Integers" [(ngModel)]="rule.value"
                                                                            [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()" />
                                                                        <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                                                    </span>
                                                                </div>
                                                            </ng-container>
                                                        </ng-container>
                                        <!-- type = number ends -->
                                        <!--TEXT FILED STARTS-->
                                                    <div class="col-3 reducePadding" *queryInput=" let rule; let field = field; type: 'string'; let onChange = onChange">
                                                        <div *ngIf=" rule.operator !== 'is null' || rule.operator !== 'is not null'">
                                                            <span class="p-input-icon-right">
                                                                <input pInputText style="width: 100%" class="w-full" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                                                <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                                            </span>
                                                        </div>
                                                    </div>
                                        <!--TEXT FILED END-->
                                        <!--TIME FILED STARTS-->
                                                    <ng-container *queryInput=" let rule; let field = field; type: 'time'; let onChange = onChange">
                                                        <div class="col-1 py-3">
                                                            <div class="flex flex-row align-items-center">
                                                                <span *ngIf="rule.checked == false" class="mb-1">Default</span>
                                                                <span *ngIf="rule.checked == true" class="mb-1">Custom</span>
                                                                <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                                                                    (ngModelChange)="ruleSwitchChanged(rule)" (ngModelChange)="onChange(rule)"  class="ml-2"></p-inputSwitch>    
                                                            </div>
                                                        </div>
                                                        <div class="col-2 reducePadding" *ngIf="!rule.checked">
                                                            <div *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null' ">
                                                                <p-calendar appendTo="body" styleClass="w-full" [showIcon]="true" showClear="true" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" 
                                                                    (ngModelChange)="onChange()" [showTime]="true" [timeOnly]="true">
                                                                </p-calendar>
                                                            </div>
                                                        </div>
                                                        <div class="col-2 reducePadding" *ngIf="rule.checked">
                                                            <div *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null'">
                                                                <span class="p-input-icon-right">
                                                                    <input style="width: 100%" class="w-full" pInputText [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                                                    <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                        <!--Time FILED END-->
                                        </div>
                                        </query-builder>
                                    </div>
                                    <div class="mt-2" style="text-align: right;">
                                    <!-- <p-button label="Close" icon="pi pi-times" styleClass="p-button-sm p-button-danger" class="mr-2" (onClick)="close()"></p-button>
                                    <p-button label="Save" icon="pi pi-save" styleClass="p-button-sm"  (onClick)="save()"></p-button> -->
                                    </div>
                                    <ng-template #nofields>
                                    <div class="error-msg">
                                        {{ errmsg }}
                                    </div>
                                    </ng-template>
                                </div>
                            </div>
                        
                            <div class="col-12 text-right mt-3">
                                <!-- <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="closeAccordian()"></button> -->
                                <!-- <button class="p-button-sm bg-blue mr-2" pButton type="button" label="CSV" (click)="downloadCSV()"></button> -->
                                <button pButton class="mr-2" type="button" pTooltip="Download CSV" (click)="DownloadContent(pagination, 'csv')" style="padding: 0.82rem 1rem;">
                                    <i class="fa-solid fa-file-csv"></i>
                                </button>
                                <button class="mr-2" pButton type="button" pTooltip="Download PDF" (click)="DownloadContent(pagination, 'pdf')" style="padding: 0.82rem 1rem;">
                                    <i class="fa-solid fa-file-pdf"></i>
                                </button>
                                <button class="bg-blue" pButton type="button" (click)="runReport(pagination)" [disabled]="!(filStartDate && filEndDate)">
                                    <i class="fa-solid fa-play mr-2"></i>
                                    <span style="font-weight: 700;">Run</span>
                                </button>
                            </div>
                        </div>
                    </ng-template>
                </p-accordionTab>
            </p-accordion>
        </div>
    </div>
    
    

    <!-- <form [formGroup]="searchsubmissionform" class="my-3" novalidate>
        <div class="grid formgrid">
            <div class="col-3 mt-4">
                <span class="p-float-label">
                    <p-calendar id="start" formControlName="createdDate" [maxDate]="maxDate" [readonlyInput]="true" styleClass="w-full" appendTo="body"></p-calendar>
                    <label for="start" class="mb-0 _required">Start Date</label>
                </span>
            </div>
            <div class="col-3 mt-4">
                <span class="p-float-label">
                    <p-calendar id="end" formControlName="submissionDate" [maxDate]="maxDate" [readonlyInput]="true" styleClass="w-full" appendTo="body"></p-calendar>
                    <label for="end" class="mb-0 _required">End Date</label>
                </span>
            </div>

            <div class="col-3 mt-4">
                <span class="p-float-label">
                    <p-dropdown id="Status" formControlName="taskStatus" class="p-inputtext-sm" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false" required="true"
                        [options]="tasks" appendTo="body">
                    </p-dropdown>
                    <label htmlFor="Status" class="_required">Select Status</label>
                </span>
            </div>

            <div class="col-3 mt-4">
                <div class="flex align-items-center justify-content-center">
                  <p-checkbox value="Delayed Submission Only" formControlName="delayedSubmission" inputId="Delayed" [binary]="true"></p-checkbox>
                  <label for="Delayed" class="mb-0 ml-2">Delayed Submission Only</label>
                </div>
            </div>

            <div class="col-6 mt-4 report">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center justify-content-center">
                            <p-inputSwitch id="isteam" formControlName="isTeam" (onChange)="isUserOrTeam($event)" styleClass="my-auto"></p-inputSwitch>
                            <label htmlFor="isteam" class="ml-2 mb-1">{{searchsubmissionform.get('isTeam').value === true ? 'Team' : 'User'}}</label>
                        </div>
                    </ng-template>
                    <ng-template pTemplate="content">

                    <span *ngIf="searchsubmissionform.get('isTeam').value === true" class="p-float-label">
                        <p-dropdown id="Team" formControlName="sumbittedTeamName" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                            (onChange)="onTeamSelect($event)" (onClear)="clearSelectedTeam()" optionLabel="teamName" [options]="teamsArr" appendTo="body">
                        </p-dropdown>
                        <label htmlFor="Team">Select Team</label>
                    </span>

                    <span *ngIf="searchsubmissionform.get('isTeam').value === false" class="p-float-label">
                        <p-dropdown id="User" formControlName="submittedBy" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                            (onChange)="onUserSelect($event)" (onClear)="clearSelectedUser()" optionLabel="firstName" [options]="usersArr" appendTo="body">
                        </p-dropdown>
                        <label htmlFor="User">Select User</label>
                    </span>
                    </ng-template>
                </p-fieldset>
            </div>
    
            <div class="col-6 mt-4 report">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center justify-content-center">
                            <p-inputSwitch id="iscategory" formControlName="isCategory" (onChange)="isFormOrCategory($event)" styleClass="my-auto"></p-inputSwitch>
                            <label htmlFor="iscategory" class="ml-2 mb-1">{{searchsubmissionform.get('isCategory').value === true ? 'Category' : 'Form'}}</label>
                        </div>
                    </ng-template>
                    <ng-template pTemplate="content">
  
                        <span *ngIf="searchsubmissionform.get('isCategory').value === false" class="p-float-label">
                            <p-dropdown id="Form" formControlName="formName" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                              (onChange)="onFormSelect($event)" (onClear)="clearSelectedForm()" optionLabel="formTitle" [options]="formList" appendTo="body">
                            </p-dropdown>
                            <label htmlFor="Form">Select Form</label>
                        </span>
   
                        <span *ngIf="searchsubmissionform.get('isCategory').value === true" class="p-float-label">
                            <p-dropdown id="User" formControlName="formCategory" class="p-inputtext-sm" [filter]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                (onChange)="resetSelectedForm()" optionLabel="category" optionValue="category" [options]="formCategories" appendTo="body">
                            </p-dropdown>
                            <label htmlFor="User">Select Category</label>
                        </span>
                    </ng-template>
                </p-fieldset>
            </div>
            
            <div class="col-12 text-right mt-2">
                <button class="p-button-sm bg-blue" pButton type="button" label="Search" (click)="getSubmissionsReports(searchsubmissionform.value, pageSize, pageIndex)" [disabled]="!searchsubmissionform.valid"></button>
            </div>
        </div>
    </form> -->

    <!-- <p-divider></p-divider> -->

<!-- Submission report table -->
    <p-table #dt #paginator [columns]="selectedColumns"
        [value]="dataSourceSubmissions" dataKey="submissionId"
        responsiveLayout="scroll" 
        scrollable="true"
        reorderableColumns="true"
        scrollHeight="calc(100vh - 50vh)"
        styleClass="p-datatable-sm p-datatable-gridlines"
        [tableStyle]="{ 'min-width': '50rem' }"
        rowExpandMode="single">

        <ng-template pTemplate="caption">
            <div class="flex flex-row justify-content-end">
                <p-multiSelect [options]="columns" [(ngModel)]="selectedColumns" optionLabel="header" (onChange)="saveTableColumns()" 
                selectedItemsLabel="{0} columns selected" [style]="{width: '18rem'}" placeholder="Choose Columns"></p-multiSelect>    
            </div>
        </ng-template>

    <!-- Header -->
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let col of columns" style="min-width: 10rem" [class]="col.class" pReorderableColumn>{{col.header}} </th>
                <th class="text-center">Download PDF </th>
                <!-- <th style="min-width: 10rem">Submitted on </th>
                <th style="min-width: 10rem">Submitted by </th>
                <th style="min-width: 10rem">Category </th>
                <th style="min-width: 10rem">Title </th>
                <th style="min-width: 10rem">Priority </th>
                <th style="min-width: 10rem">Approver </th>
                <th style="min-width: 10rem">Due Date </th>
                <th class="text-center">Audit Log </th>
                <th class="text-center">Download PDF </th> -->
            </tr>
        </ng-template>

    <!-- Table data -->
        <ng-template pTemplate="body" let-sub let-columns="columns" let-expanded="expanded">
            <tr (click)="formPreview(sub)" class="cursor-pointer">
                <td *ngFor="let col of selectedColumns" [class]="col.class">
                    <span>{{col.type === 'date' ? (sub[col.field] | date : col.format) : sub[col.field]}}</span>
                    <button *ngIf="col.type === 'button'" pButton icon="pi pi-folder-open" (click)="getAuditLog($event,sub);dt.toggleRow(sub, $event);$event.stopPropagation()" class="p-button-text p-button-rounded p-button-primary"></button>
                </td>
                <td class="text-center">
                    <button pButton icon="pi pi-download" (click)="downloadPdf($event,sub)" class="p-button-text p-button-rounded p-button-primary"></button>
                </td>
                <!-- <td>
                    {{sub.submittedOn | date: "MMM d, y, h:mm:ss a"}}
                </td>
        
                <td>
                    {{sub.submittedBy}}
                </td>
        
                <td>
                    {{sub.formCategory}}
                </td>

                <td>
                    <button *ngIf="sub.formsetName" pButton icon="pi pi-file-o" class="p-button-text p-button-rounded p-button-primary" pTooltip="sub.formsetName"></button>
                    {{ sub.formTitle }}
                </td>

                <td>
                    {{sub.priority}}
                </td>

                <td>
                    {{sub.primaryUser}}
                </td>

                <td>
                    {{sub.dueDate | date: "MMM d, y, h:mm:ss a"}}
                </td>

                <td class="text-center">
                    <button pButton icon="pi pi-folder-open" (click)="getAuditLog($event,sub);dt.toggleRow(sub, $event);$event.stopPropagation()" class="p-button-text p-button-rounded p-button-primary"></button>
                </td> -->
            </tr>
        </ng-template>
        
    <!-- Row expand -->
        <ng-template pTemplate="rowexpansion" let-element>
        <tr>
            <td [attr.colspan]="totalColumns">
                <div class="card">
                    <p-dataView #dv *ngIf="auditLogs" [value]="auditLogs">
                        <ng-template let-form pTemplate="listItem">
                            <div class="col-12 form-list">
                                <div class="flex flex-row align-items-center justify-content-between p-2 gap-3">
                                    <div class="form-subtext">{{ form.updatedTime | date: "MMM d, y, h:mm:ss a" }}</div>
                                    <div class="form-subtext">{{ form.message }}</div>
                                    <button pButton label="Form" class="p-button-sm p-button-outlined" (click)="$event.stopPropagation();navigateToFormRendererForAudit(element, form.logId)"></button>
                                </div>
                            </div>
                        </ng-template>
                        <ng-template let-form pTemplate="empty">
                            <div class="col text-center">
                                <span>No results found.</span>
                            </div>
                        </ng-template>
                    </p-dataView>
                </div>
            </td>
        </tr>
        </ng-template>

    <!-- Paginator -->
        <ng-template pTemplate="summary">
            <p-paginator class="p-0" [rows]="10" [totalRecords]="reportLength" [showJumpToPageDropdown]="false" dropdownAppendTo="body"
                (onPageChange)="runReport($event)" [showPageLinks]="true" [rowsPerPageOptions]="[10,25,50]">
            </p-paginator>
        </ng-template>

    <!-- No data -->
        <ng-template pTemplate="emptymessage">
            <tr>
                <td class="text-center" style="font-weight: bold;" [attr.colspan]="totalColumns">No matching data.</td>
            </tr>
        </ng-template>

    </p-table>

</div>

<!-- Create/Update Report -->
<p-dialog [header]="reportState" [(visible)]="showReportDialog" (onHide)="cancelCreateReportDialog(reportId)" [modal]="true" [style]="{ width: '60vw' }" [draggable]="false" [resizable]="false">
    <div class="card rep">
        <div class="grid">
    <!-- step 1 -->
            <div *ngIf="step1" class="col-6 mt-4">
                <span class="p-float-label">
                    <input pInputText id='Name' [(ngModel)]="reportName" class="w-full" type="text" required="true" [autofocus]="true" pAutoFocus>
                    <label htmlFor="Name" class="_required">Report Name</label>
                </span>
            </div>
            <div *ngIf="step1" class="col-6 mt-4 desc">
                <div class="p-float-label">
                    <textarea id="Description" pInputTextarea [(ngModel)]="reportDescription" rows="1" cols="30" class="w-full" required="true"></textarea>
                    <label htmlFor="Description" class="_required">Report Description</label>
                </div>
            </div>
            <div *ngIf="step1" class="col-12">
                <p-fieldset>
                    <ng-template pTemplate="header">Filter On</ng-template>
                    <ng-template pTemplate="content">
                        <div class="grid">
                            <div *ngIf="step1" class="col-2 text-center my-auto p-1">
                                <!-- <span class="p-float-label">
                                    <p-multiSelect id="field" [options]="filterFieldsArr" [(ngModel)]="filterFields" optionLabel="label" optionValue="label" 
                                        (onChange)="onFilterFieldChange($event)" styleClass="w-full" appendTo="body"></p-multiSelect>
                                    <label htmlFor="field">Select Filter Fields</label>
                                </span> -->
                                <p-checkbox id="status" [(ngModel)]="statusField" binary="true"></p-checkbox>
                                <label for="status" class="mb-0 ml-2">Status</label>
                            </div>
                            <div *ngIf="step1" class="col-4 my-auto p-1 text-center border-right-1">
                                <p-checkbox id="sub" [(ngModel)]="delayedSubField" binary="true"></p-checkbox>
                                <label for="sub" class="mb-0 ml-2">Delayed Submission Only</label>
                            </div>
                            <div *ngIf="step1" class="col-3 my-auto p-1 border-right-1">
                                <div class="flex flex-row justify-content-around w-full">
                                    <div class="flex align-items-center">
                                        <p-radioButton value="Form" [(ngModel)]="formOrCategoryField" inputId="forms" (onClick)="onFormOrCatSelect($event)"></p-radioButton>
                                        <label for="forms" class="ml-2 mb-0">Form</label>
                                    </div>
                                    <div class="flex align-items-center">
                                        <p-radioButton value="Category" [(ngModel)]="formOrCategoryField" inputId="category" (onClick)="onFormOrCatSelect($event)"></p-radioButton>
                                        <label for="category" class="ml-2 mb-0">Category</label>
                                    </div>
                                </div>
                                <!-- <p-divider styleClass="my-2" layout="vertical"></p-divider> -->
                            </div>
                            <div *ngIf="step1" class="col-3 my-auto p-1">
                                <div class="flex flex-row justify-content-around w-full">
                                    <div class="flex align-items-center">
                                        <p-radioButton value="User" [(ngModel)]="userOrTeamField" inputId="usr"></p-radioButton>
                                        <label for="usr" class="ml-2 mb-0">User</label>
                                    </div>
                                    <div class="flex align-items-center">
                                        <p-radioButton value="Team" [(ngModel)]="userOrTeamField" inputId="team"></p-radioButton>
                                        <label for="team" class="ml-2 mb-0">Team</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </p-fieldset>
            </div>
            <div *ngIf="step1" class="col-12 text-right">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="showReportDialog = false;"></button> <!-- Not calling cancelCreateReportDialog() because we have onHide() -->
                <button *ngIf="formOrCategoryField !== 'Form'" class="p-button-sm bg-blue" pButton type="button" [label]="reportState === 'Update' ? 'Update' : 'Save'" (click)="reportState === 'Update' ? updateReport() : createReport()" [disabled]="!(reportName && reportDescription)"></button>
                <button *ngIf="formOrCategoryField === 'Form'" class="p-button-sm bg-blue" pButton type="button" label="Next" (click)="gotoStep2()" [disabled]="!(reportName && reportDescription)"></button>
            </div>
        <!-- step 2 -->
            <div *ngIf="step2" class="col-12 mt-4">
                <span class="p-float-label">
                    <p-dropdown id="Form" [(ngModel)]="selectedForm" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                        (onChange)="onFormSelect($event)" optionLabel="formTitle" optionValue="formId" [options]="formList" appendTo="body">
                    </p-dropdown>
                    <label htmlFor="Form" class="_required">Select Form</label>
                </span>
            </div>
            <div *ngIf="formFieldsArr && step2" #pnl class="col-12">
                <span class="p-float-label">
                    <p-tree [value]="formFieldsArr" selectionMode="checkbox" class="w-full" [(selection)]="selectedFields" scrollHeight="300px"></p-tree>
                </span>
            </div>
            <div *ngIf="step2" class="col-12 text-right">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Back" (click)="backToStep1()"></button>
                <button class="p-button-sm bg-blue" pButton type="button" label="Next" (click)="gotoStep3()" [disabled]="!selectedForm"></button>
            </div>
            <!-- step 3 -->
            <div *ngIf="step3" class="col-12">
                    <div style="text-align: left" class="query-builder" *ngIf="(filConfig.fields | json) != '{}'; else nofields">
                        <query-builder [(ngModel)]="formQuery" [ngModelOptions]="{ standalone: true }" [config]="filConfig" [allowRuleset]="allowRuleset"
                                [allowCollapse]="allowCollapse" [persistValueOnFieldChange]="persistValueOnFieldChange">
                            <ng-container *queryButtonGroup="let ruleset;let addRule = addRule;let addRuleSet = addRuleSet;let removeRuleSet = removeRuleSet ;let index = index">
                                <p-button [disabled]="isDBdisabled" icon="pi pi-plus" styleClass="p-button-info" class="mr-3" (onClick)="addRule();addRulesett(ruleset)"></p-button>
                                <p-button [disabled]="isDBdisabled" icon="pi pi-plus-circle" styleClass="p-button-info" class="mr-3" *ngIf="addRuleSet" (onClick)="addRuleSet()"></p-button>
                                <p-button [disabled]="isDBdisabled" icon="pi pi-minus-circle" styleClass="p-button-danger" class="mr-3 mt-1" *ngIf="removeRuleSet" (onClick)="removeRuleSet()"></p-button>
                            </ng-container>
                            <ng-container *queryArrowIcon>
                            <i class="pi pi-angle-right" ngClass="mat-arrow-icon"></i>
                            </ng-container>
                        <!--AND / OR STARTS-->
                        <ng-container *querySwitchGroup="let ruleset; let onChange = onChange" >
                            <div style=" margin: 5px 0px 0px 5px !important;">
                                <p-selectButton [disabled]="isDBdisabled" [options]="stateOptions" class="selectBtn" [(ngModel)]="ruleset.condition" [ngModelOptions]="{ standalone: true }"
                                (ngModelChange)="onChange($event)" optionLabel="label" optionValue="value"></p-selectButton>
                            </div>
                        </ng-container>
                        <!--AND / OR ENDS-->
                        <div class="grid" >
                            <div class="col-1 reducePadding" *queryRemoveButton="let rule; let removeRule = removeRule">
                            <p-button [disabled]="isDBdisabled" icon="pi pi-trash" styleClass="delQueryBtn p-button-danger" (onClick)="removeRule(rule)"></p-button>
                            </div>
                            <div class="col-4 reducePadding" *queryEntity=" let rule; let entities = entities; let onChange = onChange">
                            <p-dropdown [disabled]="isDBdisabled" [options]="entities" [(ngModel)]="rule.entity" optionLabel="name" optionValue="value"
                                [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange($event, rule)" [autoDisplayFirst]="true" appendTo="body"></p-dropdown>
                            </div> 
                            <!--SELECT FILEDS STARTS--> 
                        <div class="col-4 reducePadding" *queryField="let rule;let fields = fields;let onChange = onChange;let getFields = getFields">
                            <p-dropdown [disabled]="isDBdisabled" [options]="getFields(rule.entity)" [(ngModel)]="rule.field" optionLabel="name" optionValue="value"
                            [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange($event, rule)" [autoDisplayFirst]="true" (onChange)="getData(rule)"
                            appendTo="body" class="formsNodeOp"></p-dropdown>
                        </div>
                        <!--SELECT FILEDS ENDS-->
                        <!--SELECT Operation STARTS-->
                        <div class="col-3 reducePadding" *queryOperator="let rule;let operators = operators;let onChange = onChange">
                            <p-dropdown [disabled]="isDBdisabled" [options]="operators" [(ngModel)]="rule.operator" 
                            [ngModelOptions]="{ standalone: true }" (ngModelChange)="ruleOperationChanged(rule)" (ngModelChange)="onChange(rule)" 
                            [autoDisplayFirst]="true" appendTo="body" class="formsNodeOp"></p-dropdown>
                        </div>
                        <!--SELECT operation ENDS-->
                        <!--Select for radio or checkbox or dropdown STARTS-->
                        <ng-container *queryInput=" let rule; let field = field; let options = options; type: 'category'; let onChange = onChange ">
                        <div class="col-1 reducePadding1">
                            <span *ngIf="rule.checked == false" class="mb-1">Default</span>
                            <span *ngIf="rule.checked == true" class="mb-1">Custom</span>
                            <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                                (ngModelChange)="ruleSwitchChangedDropdown(rule)" (ngModelChange)="onChange(rule)"  class="ml-2"></p-inputSwitch>
                        </div>
                        <div class="col-3 reducePadding" *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null'">
                            <div *ngIf="!rule.checked">
                            <p-dropdown [options]="options"  (ngModelChange)="onChange(rule)" [(ngModel)]="rule.value"
                            optionLabel="name" optionValue="value" [ngModelOptions]="{ standalone: true }"
                            (ngModelChange)="onChange()" [autoDisplayFirst]="true" appendTo="body"></p-dropdown>
                            </div>
                            <div *ngIf="rule.checked">     
                                <span class="p-input-icon-right">
                            <input pInputText [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                            <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                            </span>
                        </div>
                        </div>
                        </ng-container>
                        <!--Select for radio or checkbox ENDS-->
                        <!-- type is date STRAT -->
                                    <ng-container *queryInput="let rule; let field = field; type: 'date'; let onChange = onChange;let checked = checked;">
                                    <div class="col-1 reducePadding1">
                                        <span *ngIf="rule.checked == false" class="mb-1">Default</span>
                                        <span *ngIf="rule.checked == true" class="mb-1">Custom</span>
                                    <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                                        (ngModelChange)="ruleSwitchChanged(rule)" (ngModelChange)="onChange(rule)"  class="ml-2"></p-inputSwitch>
                                    </div>
                                    <ng-container *ngIf="rule.operator === 'between' || rule.operator === 'not between'">
                                        <div class="col-3 reducePadding" *ngIf="!rule.checked">
                                        <p-calendar selectionMode="range" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }"
                                        (ngModelChange)="onChange()" appendTo="body" [iconDisplay]="'input'" [showIcon]="true"></p-calendar>
                                        </div>
                                        <div class="col-3 reducePadding" *ngIf="rule.checked">     
                                            <span class="p-input-icon-right">
                                            <input pInputText style="width: 160px;" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                            <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                        </span>
                                        </div>
                                    </ng-container>
                                    
                                    <div class="col-3 reducePadding" *ngIf=" rule.operator !== 'between' && rule.operator !== 'not between' && rule.operator !== 'is null' && rule.operator !== 'is not null' && !rule.checked">
                                        <p-calendar [(ngModel)]="rule.value" [showIcon]="true" [ngModelOptions]="{ standalone: true }"
                                        (ngModelChange)="onChange()" appendTo="body"></p-calendar>
                                    </div>
                                        <div class="col-3 reducePadding" *ngIf=" rule.operator !== 'between' && rule.operator !== 'not between' && rule.operator !== 'is null' && rule.operator !== 'is not null' && rule.checked">
                                            <span class="p-input-icon-right">
                                            <input pInputText style="width: 100%"  [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                            <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)" ></i> -->
                                            </span>
                                        </div>
                                    </ng-container>
                        <!-- type = date ENDS -->
                                        <!-- type = number -->
                                        <ng-container *queryInput=" let rule; let field = field; type: 'number'; let onChange = onChange">
                                        <ng-container *ngIf="rule.operator === 'between' || rule.operator === 'not between'">
                                            <div class="col-2 reducePadding">
                                            <span class="p-input-icon-right">
                                                <input pInputText  style="width: 100%" [(ngModel)]="rule.value[0]" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                                <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value[0],1)"></i> -->
                                            </span>
                                            </div>
                                            <div style="margin-top:20px !important;">To</div>
                                            <div class="col-2 reducePadding">
                                            <span class="p-input-icon-right">
                                            <input pInputText  style="width: 100%;" [(ngModel)]="rule.value[1]" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                            <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value[1],2)"></i> -->
                                            </span>
                                        </div>
                                        </ng-container>
                                        <ng-container *ngIf="rule.operator !== 'between' && rule.operator !== 'not between'">
                                            <div class="col-4 reducePadding">
                                            <span class="p-input-icon-right">
                                                <input style="width: 100%" type="text" pInputText  placeholder="Integers" [(ngModel)]="rule.value"
                                                [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()" />
                                                <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                            </span>
                                            </div>
                                            </ng-container>
                                        </ng-container>
                                        <!-- type = number ends -->
                
                                    <!--TEXT FILED STARTS-->
                                    <div class="col-4 reducePadding" *queryInput=" let rule; let field = field; type: 'string'; let onChange = onChange">
                                        <div *ngIf=" rule.operator !== 'is null' || rule.operator !== 'is not null'">
                                            <span class="p-input-icon-right">
                                            <input pInputText style="width: 100%" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                            <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                            </span>
                                        </div>
                                    </div>
                                    <!--TEXT FILED END-->
                                    <!--TIME FILED STARTS-->
                                    <ng-container *queryInput=" let rule; let field = field; type: 'time'; let onChange = onChange">
                                    <div class="col-1 reducePadding1">
                                        <span *ngIf="rule.checked == false" class="mb-1">Default</span>
                                        <span *ngIf="rule.checked == true" class="mb-1">Custom</span>
                                    <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                                        (ngModelChange)="ruleSwitchChanged(rule)" (ngModelChange)="onChange(rule)"  class="ml-2"></p-inputSwitch>
                                    </div>
                                    <div class="col-3 reducePadding" *ngIf="!rule.checked">
                                    <div *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null' ">
                                        <p-calendar appendTo="body" [showIcon]="true" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" 
                                        (ngModelChange)="onChange()" [showTime]="true"  [timeOnly]="true">
                                        </p-calendar>
                                    </div>
                                    </div>
                                    <div class="col-3 reducePadding" *ngIf="rule.checked">
                                    <div *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null'">
                                        <span class="p-input-icon-right">
                                        <input style="width: 100%" pInputText [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                        <!-- <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i> -->
                                        </span>
                                    </div>
                                </div>
                                    </ng-container>
                                    <!--Time FILED END-->
                        </div>
                        </query-builder>
                    </div>
                    <div class="mt-2" style="text-align: right;">
                    <!-- <p-button label="Close" icon="pi pi-times" styleClass="p-button-sm p-button-danger" class="mr-2" (onClick)="close()"></p-button>
                    <p-button label="Save" icon="pi pi-save" styleClass="p-button-sm"  (onClick)="save()"></p-button> -->
                    </div>
                    <ng-template #nofields>
                    <div class="error-msg">
                        {{ errmsg }}
                    </div>
                    </ng-template>
            </div>
            <div *ngIf="step3" class="col-12 text-right">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Back" (click)="backToStep2()"></button>
                <!-- <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="createReport()"></button> -->
                <button class="p-button-sm bg-blue" pButton type="button" [label]="reportState === 'Update' ? 'Update' : 'Save'" (click)="reportState === 'Update' ? updateReport() : createReport()"></button>
            </div>
        </div>
    </div>
    
</p-dialog>

<!-- old filter code -->
<!-- <div class="col-6">
    <p-accordion styleClass="mb-3">
        <p-accordionTab [selected]="true" >
            <ng-template pTemplate="header">
                <span>Report Filter</span>
            </ng-template>
            
            <ng-template pTemplate="content">
                <form [formGroup]="filterReportForm" novalidate>
                    <div class="grid formgrid">
                        <div class="col-6 mt-4">
                            <span class="p-float-label">
                                <p-calendar id="start" formControlName="startDate" [maxDate]="maxDate" [readonlyInput]="true" styleClass="w-full" appendTo="body"></p-calendar>
                                <label for="start" class="mb-0 _required">Start Date</label>
                            </span>
                        </div>
                        <div class="col-6 mt-4">
                            <span class="p-float-label">
                                <p-calendar id="end" formControlName="endDate" [maxDate]="maxDate" [readonlyInput]="true" styleClass="w-full" appendTo="body"></p-calendar>
                                <label for="end" class="mb-0 _required">End Date</label>
                            </span>
                        </div>
                        <div class="col-6 mt-4">
                            <span class="p-float-label">
                                <p-dropdown id="Status" formControlName="formStatus" styleClass="w-full" [autoDisplayFirst]="false"
                                    [options]="tasks" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="Status" >Select Status</label>
                            </span>
                        </div>
                        
                        <div class="col-6 mt-4">
                            <div class="flex align-items-center">
                                <p-checkbox value="Delayed Submission Only" formControlName="delayedSubmissionOnly" inputId="Delayed" [binary]="true"></p-checkbox>
                                <label for="Delayed" class="mb-0 ml-2">Delayed Submission Only</label>
                            </div>
                        </div>

                        <div class="col-12 mt-4 report">
                            <p-fieldset>
                                <ng-template pTemplate="header">
                                    <div class="flex align-items-center justify-content-center">
                                        <p-inputSwitch id="isteam" formControlName="isTeam" (onChange)="isUserOrTeam($event)" styleClass="my-auto"></p-inputSwitch>
                                        <label htmlFor="isteam" class="ml-2 mb-1">{{filterReportForm.get('isTeam').value === true ? 'Team' : 'User'}}</label>
                                    </div>
                                </ng-template>
                                <ng-template pTemplate="content">
            
                                <span *ngIf="filterReportForm.get('isTeam').value === true" class="p-float-label">
                                    <p-dropdown id="Team" formControlName="submittedTeam" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                        (onChange)="onTeamSelect($event)" (onClear)="clearSelectedTeam()" optionLabel="teamName" [options]="teamsArr" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Team">Select Team</label>
                                </span>
            
                                <span *ngIf="filterReportForm.get('isTeam').value === false" class="p-float-label">
                                    <p-dropdown id="User" formControlName="submittedUser" class="p-inputtext-sm" [filter]="true" [showClear]="true" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                        (onChange)="onUserSelect($event)" (onClear)="clearSelectedUser()" optionLabel="firstName" [options]="usersArr" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="User">Select User</label>
                                </span>
                                </ng-template>
                            </p-fieldset>
                        </div>
                    
                        <div class="col-12 text-right mt-2">
                        comment    <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="closeAccordian()"></button>
                            <button class="p-button-sm bg-blue mr-2" pButton type="button" label="CSV" (click)="downloadCSV()"></button>
                            <button class="p-button-sm bg-blue mr-2" pButton type="button" label="PDF" (click)="downloadPDF()"></button>
                            <button class="p-button-sm bg-blue" pButton type="button" label="Run" (click)="runReport()" [disabled]="filterReportForm.invalid"></button>
                        </div>
                    </div>
                </form>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div> -->