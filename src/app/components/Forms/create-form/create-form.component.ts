import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';

@Component({
  selector: 'app-create-form',
  templateUrl: './create-form.component.html',
  styleUrls: ['./create-form.component.scss']
})
export class CreateFormComponent implements OnInit {
  createform: FormGroup;
  errmsg: string;
  isMasterform: boolean;
  isUpdateMasterData:boolean;
  categoryDisplay:any;
  formTypeArr: any = [{'label': 'Form', 'key': 'form'},{'label': 'Nested Form', 'key': 'nestedForm'},{'label': 'Wizard', 'key': 'wizard'}]
  iconsArr: any;
  selectedIcon: string = 'description';
  newCategory: any;
  saveTemplate: boolean = false;

  constructor(
    private fb: FormBuilder,
    private formservice: FormsService,
    public data: DynamicDialogConfig,
    private messageService: MessageService,
    private ref: DynamicDialogRef,
    private router: Router
  ) {}

  ngOnInit() {
    this.getFormsCategory();
    this.formservice.getAllIcons().subscribe(data => {this.iconsArr = data});
    this.createform = this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(100)]],
      shortname: [{value: '', disabled: true}, Validators.required],
      description: ['', Validators.maxLength(200)],
      formCategory:['', Validators.required],
      categoryType:[''],
      formtype: [''],
      usage: ['masterdata'],
      icon: [{ value: '' }, Validators.required],
      isTemplate: [false],
    });

    if (this.data.data.isNewForm) {
      if (this.data.data) {
        this.createform.patchValue({
          formtype: this.data.data.formType === 'Templates' ? 'form' : this.data.data.formType,
          categoryType: this.data.data.formType === 'Templates' ? this.data.data.formType : null,
        });
        // if (this.data.data.formType === 'Templates') {
        //   this.createform.controls['formCategory'].clearValidators();
        //   this.createform.controls['formCategory'].patchValue('Templates');
        //   this.createform.controls['formCategory'].updateValueAndValidity();
        // }
      }
    }

    if (this.data.data.isCopyForm) {
      if (this.data.data.form) {
        this.createform.patchValue({
          title: `${this.data.data.form.formTitle} Copy`,
          description: this.data.data.form.description,
          formCategory:this.data.data.form.category,
          shortname: `${this.data.data.form.formName}copy`,
          formtype: this.data.data.form.formType === 'Templates' ? 'form' : this.data.data.form.formType,
          // icon: this.data.form.avatar,
          categoryType: this.data.data.form.formType
        });
        this.selectedIcon = this.data.data.form.avatar;
      }
    }

    if (this.data.data.isUpdateForm) {
      if (this.data.data.form) {
        this.createform.patchValue({
          title: `${this.data.data.form.formTitle}`,
          description: this.data.data.form.description,
          shortname: `${this.data.data.form.formName}`,
          formCategory:this.data.data.form.category,
          formtype: this.data.data.form.formType === 'Templates' ? 'form' : this.data.data.form.formType,
          // icon: this.data.form.avatar,
          categoryType: this.data.data.form.formType
        });
        this.selectedIcon = this.data.data.form.avatar;
      }
    }
    // if(this.data.data.isupdatemasterdate){
    //   this.isUpdateMasterData=true
    //   if(this.data.data.form){
    //     this.createform.patchValue({
    //       title:`${this.data.data.form.formName}`,
    //       shortname: `${this.data.data.form.formName}`,
    //       description: this.data.data.form.description,
    //     });
    //     this.createform.get('title').disable();
    //     this.selectedIcon=`${this.data.data.form.avatar}`;
    //   }
    // }
    // if (this.data.data.type === 'masterdata') {
    //   this.isMasterform = true;
    //   this.createform.patchValue({
    //     formCategory:'Masterdata'
    //   });
    //   if(this.data.data.icon==='value'){
    //    this.selectedIcon='dataset';
    //   }
    // }
    // if (this.data.data.type === 'form') {
    //    this.createform.get('usage').setValidators(null);
    //    this.createform.get('usage').updateValueAndValidity();
    //   //  this.picked_icon='description';
    // }
    this.title.valueChanges.subscribe(value => {
      this.createform.patchValue({
        shortname: value?.split(' ').join('').toLowerCase()
      });
    });
    // if(!this.data.data.isupdatemasterdate){
    //   this.title.valueChanges.subscribe(value => {
    //     this.createform.patchValue({
    //       shortname: value?.split(' ').join('').toLowerCase()
    //     });
    //   });}
  }
  
  get title() {
    return this.createform.get('title');
  }
  get shortname() {
    return this.createform.get('shortname').value;
  }

  getFormsCategory() {
    this.formservice.getFormsCategories().subscribe((data: any) => {
      const res = data;
      if (res.status === "Success") {
        this.categoryDisplay = res.category;
      }
    })
  }

  CreateForm() {
    // if (this.data.data.iscopyform) {
    //   this.createform.controls['shortname'].patchValue(`${this.data.data.form.formName}copy`);
    //   this.createform.controls['shortname'].updateValueAndValidity();
    // }
    if (this.data.data.formType === 'nestedForm' || this.data.data.formType === 'masterdata') {
      this.createform.controls['isTemplate'].patchValue(false);
      this.createform.controls['isTemplate'].updateValueAndValidity();
    }
    if (this.createform.get('isTemplate').value) {
      this.createform.controls['categoryType'].patchValue('Templates');
      this.createform.controls['categoryType'].updateValueAndValidity();
      // this.createform.controls['formCategory'].patchValue('Templates');
      // this.createform.controls['formCategory'].updateValueAndValidity();
    }
    this.formservice.createform(this.createform.getRawValue(), this.selectedIcon, this.data.data)
      .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          const obj = {
            formId: response.formId,
            category: response.category,
            type: this.data.data.type
          }
          this.ref.close(obj);
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!' });
          // if (this.data.data?.form?.formType === 'Templates' || this.data.data?.formType === 'Templates') {
          //   this.router.navigate(['/templates']);
          // } else {
            this.router.navigate(['/forms']);
          // }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }
  UpdateForm() {
    this.formservice.updateform(this.createform.getRawValue(), this.selectedIcon, this.data.data)
      .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!' });
          this.ref.close(response);
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }
  // UpdateMaterData() {    not using this method
  //   const fId=this.data.data.form.formId;
  //   const fName=this.createform.getRawValue().shortname; //change to fomr value
  //   const fdesc=this.createform.getRawValue().description;
  //   const fcategory=this.createform.getRawValue().formCategory;
  //   const icon=this.selectedIcon;
  //   const type='masterdata';

  //   this.formservice.masterdateupdate(fId,fName,fcategory,fdesc,icon,type).subscribe((response)=>{
  //     if (response.status.toLowerCase() === 'success') {
  //       this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!' });
  //       this.ref.close(response);
  //     } else {
  //       this.errmsg = response.error;
  //       this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
  //     }
  //   })
  // }

  resetForm() {
    this.createform.reset();
    this.ref.close(null)
  }

  selectIcon(icon: string, element?: any) {
    this.selectedIcon = icon;
    this.createform.get('icon').setValue(this.selectedIcon);
    element.hide();
  }

  createCategory(element: any) {
     this.formservice.createCategory(this.newCategory,null).subscribe(res => {
       const response = res;
       if (response.error === '') {
         if (response.status === 'Success') {
          element.hide();
          this.getFormsCategory();
          this.createform.patchValue({
            formCategory: response.category,
          });
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Category created!' });
         }
       } else {
         this.errmsg = response.error;
         this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
       }
     });
  }

  resetCatergory(element?: any) {
    if (element) {
      element.hide();
    }
    this.newCategory = null;
  }

  // onChangeTemplate(event: any) {
  //   if (event.checked) {
  //     this.createform.controls['formCategory'].clearValidators();
  //     this.createform.controls['formCategory'].patchValue('Templates');
  //     this.createform.controls['formCategory'].disable();
  //     this.createform.controls['formCategory'].updateValueAndValidity();
  //   } else {
  //     this.createform.controls['formCategory'].setValidators([Validators.required]);
  //     this.createform.controls['formCategory'].patchValue(null);
  //     this.createform.controls['formCategory'].enable();
  //     this.createform.controls['formCategory'].updateValueAndValidity();
  //   }
  // }

  // onChangeCategory(event: any) {
  //   if (event?.value?.toLowerCase() === 'templates') {
  //     this.createform.controls['formCategory'].clearValidators();
  //     this.createform.controls['formCategory'].patchValue('Templates');
  //     this.createform.controls['formCategory'].disable();
  //     this.createform.controls['formCategory'].updateValueAndValidity();

  //     this.createform.controls['isTemplate'].patchValue(true);
  //     this.createform.controls['isTemplate'].updateValueAndValidity();
  //   }
  // }

}
