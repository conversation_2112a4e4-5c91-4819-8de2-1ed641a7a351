import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, EventEmitter, OnDestroy, OnInit, Renderer2, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import { ReteService } from 'src/app/services/rete.service';
import { Location } from '@angular/common'
import { BarcodeDialogComponent } from '../barcode-dialog/barcode-dialog.component';
import { ReportService } from 'src/app/services/report.service';
import { SettingsService } from 'src/app/services/settings.service';
import { FormioComponent } from '@formio/angular';
import { Formio } from 'formiojs';
import { Subscription, delay } from 'rxjs';
import { LoaderService, LoaderState } from 'src/app/services/loader.service';
import WebFont from 'webfontloader';

declare var less: any;

@Component({
  selector: 'app-form-preview',
  templateUrl: './form-preview.component.html',
  styleUrls: ['./form-preview.component.scss'],
  providers: [DialogService],
  encapsulation: ViewEncapsulation.None
})
export class FormPreviewComponent implements OnInit, OnDestroy {
  @ViewChild('formio', { static: false }) formioComponent: FormioComponent;
  // @ViewChild('formio', { static: true }) formioElement: ElementRef | undefined;
  private subscription: Subscription;
  public i18nAll: any;
  public submissiondata: any;
  public formid: any;
  public form: any;
  public refreshForm: any;
  public errmsg: any;
  // public submission: any;
  public masterdataResources: any;
  public formUserData: any;
  public languages: any = [];
  public currentLanguage: any;
  public allLanguage: any;
  public seletedLanguage: any;
  public translationLanguages: any[] = [];
  // public displayIcon: boolean;
  public routerData: any;
  public device: string = 'Desktop';
  public mode: string = 'portrait';
  public customHeight: number;
  public customWidth: number;
  public isCustomDevice: boolean = false;
  public hideDeviceIcons: boolean = false;
  ref: DynamicDialogRef | undefined;
  formTitle: string;
  versionArr: any[] = [];
  currentVersion: number;
  readonly : boolean = false;
  lastUpdated: any;
  lastUpdatedBy: string;
  isSubmitting: boolean = false;
  formVersion: string;
  formVersionComment: string;
  formVersionDate: string;
  showSpinner: boolean = false;
  dataPreview: string = '';
  iframeElement: HTMLIFrameElement;
  releaseType: string;
  landsacpe: string;
  cssArr: any[] = [
    "assets/fomantic-ui/definitions/globals/site.less",
    "assets/fomantic-ui/definitions/elements/button.less",
    "assets/fomantic-ui/definitions/modules/checkbox.less",
    "assets/fomantic-ui/definitions/modules/tab.less",
    "assets/fomantic-ui/definitions/collections/menu.less",
    "assets/fomantic-ui/definitions/elements/segment.less",
    "assets/fomantic-ui/definitions/elements/header.less",
    "assets/fomantic-ui/definitions/elements/step.less",
    "assets/fomantic-ui/definitions/modules/dropdown.less",
    "assets/fomantic-ui/definitions/collections/form.less",
  ];
  linkRefs: HTMLLinkElement[] = [];
  isCard: boolean = false;
  themeObj: any;

  constructor(
    private formsservice: FormsService,
    private reteservice: ReteService,
    // public translate: TranslateService,
    private httpClient: HttpClient,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private messageService: MessageService,
    public dialogService: DialogService,
    private reportService: ReportService,
    private settingservice: SettingsService,
    private loaderService: LoaderService,
    private renderer: Renderer2,
    private el: ElementRef
  ) {
    // localStorage.setItem('sementic', JSON.stringify(semantic));
    this.currentLanguage = localStorage.getItem("language");
    this.route.paramMap.subscribe((params: ParamMap) => {
      this.routerData = decodeURIComponent(params.get('data') || "");
      this.routerData = JSON.parse(this.routerData);
      this.submissiondata = this.routerData.submission && Object.keys(this.routerData.submission).length > 0 ? this.routerData.submission : null;
    });
    this.landsacpe = localStorage.getItem('landscape');
  }

  ngOnInit() {
    this.loadSemanticTemplate().then((semanticTemplate: any) => {
      Formio.use(semanticTemplate?.default || semanticTemplate);
    });
    this.loadCustomCss();
    // const link = this.renderer.createElement('link');
    // link.rel = 'stylesheet';
    // link.href = 'assets/js/semantic_new.min.css';
    // this.renderer.appendChild(document.head, link);

    this.languages = [];
    this.refreshForm = new EventEmitter();
    // when come from Flow Test data
    if (this.routerData.wfId) {
      this.getFlowTestData();
    } 
    // else if (this.routerData.form) {
    //   this.masterdataResources = this.routerData.masterdata;
    //   this.form = this.getMasterdDataresource(
    //     this.routerData.form,
    //     "dataSrc",
    //     "masterdata"
    //   );
    // }

    // when come from Share Dialog
    if (this.routerData?.shareId) {
      this.getShaerdSubmissionData();
    }

    // when come from Version Dialog
    if (this.routerData?.version) {
      this.getFormDataOnVersion(this.routerData.version);
      this.versionArr = this.routerData.versionArr;
      this.currentVersion = this.routerData.version;
    }

    // when come from MasterData
    if (this.routerData.type === 'masterdata') {
      this.getDataOfMasterData();
    }

    // when come from Reports
    if (this.routerData?.isReport) {
      if (this.routerData.submissionId) { 
        this.getReportSubmissionData();
      } else if (this.routerData.auditId) {
        this.getAuditlogDetails();
      }
    }

    // when come from form-builder
    if (this.routerData.isPreview) {
      this.getFormData();
    }

    // calling to get language array
    // this.getLanguages();
    
    // tslint:disable-next-line:prefer-const
    let rendererObj = this;
    setTimeout(function () {
      document.addEventListener(
        "customScan",
        function (event: any) {
          // tslint:disable-next-line:prefer-const
          rendererObj.openSimulateScanDialog(event.detail);
        },
        false
      );
    }, 200);
    this.subscription = this.loaderService.loaderState.pipe(
      delay(0),
    ).subscribe((state: LoaderState) => {
      this.showSpinner = state.show;
    });
  }

  loadCustomCss() {
    const semanticlink = this.renderer.createElement('link');
    semanticlink.rel = 'stylesheet';
    semanticlink.href = 'assets/js/semantic_new.min.css';
    this.renderer.appendChild(document.head, semanticlink);

    let link;
    for (let i = 0; i < this.cssArr.length; i++) {
      const link = this.renderer.createElement('link');
      link.rel = 'stylesheet/less';
      link.type = 'text/css';
      link.href = this.cssArr[i];
      this.renderer.appendChild(document.head, link);
    }
    // const script = this.renderer.createElement('script');
    // script.type = 'text/javascript';
    // script.text = `
    //   window.less = {
    //     async: true,
    //     environment: 'production',
    //     fileAsync: false,
    //     onReady: true,
    //     useFileCache: true
    //   };
    // `;
    // this.renderer.appendChild(document.head, script);
    // if (typeof less !== 'undefined' && typeof less.modifyVars === 'function') {
    //   less.modifyVars({});
    // } else {
    //   console.warn('Less.js or less.modifyVars is not available.');
    // }

    //   this.cssArr.forEach(href => {
    //   const link = document.createElement('link');
    //   link.rel = 'stylesheet/less';
    //   link.type = 'text/css';
    //   link.href = href;
    //   document.head.appendChild(link);
    //   this.linkRefs.push(link);
    // });

    // Initialize less config and render
    (window as any).less = {
      async: true,
      environment: 'production',
      fileAsync: false,
      onReady: true,
      useFileCache: true,
    };

    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/less@4.1.3/dist/less.min.js';
    script.onload = () => {};
    document.head.appendChild(script);
  }

  loadSemanticTemplate() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'assets/js/semantic.js';
      script.onload = () => resolve((window as any).semantic);
      script.onerror = reject;
      document.body.appendChild(script);
    });
  }

  getFormData() {
    this.showSpinner = true;
    this.formsservice.getformDataWithMasterData(this.routerData.formId, this.routerData.type, true)
    .subscribe({ 
      next:(response) =>
      {
        if (response?.status?.toLowerCase() === 'success') {
          if (response?.formData) {
            this.showSpinner = false;
            this.form = response.formData;
            this.formid = response.formId;
            this.formTitle = response.formTitle;
            this.releaseType = response?.releaseType;
            this.formVersion = response.version ? this.getVersionInfo(response.version, response?.isDraft) : null;
            this.formVersionComment =  response?.versionComments?.length > 0 ? response?.versionComments : null;
            this.formVersionDate = response.updatedDate
            // this.lastUpdated = response.updatedDate;
            this.getLanguages(response.translations);
            this.masterdataResources = response.masterdata;
            localStorage.setItem("resourceData",JSON.stringify(this.masterdataResources));
            this.form = this.getMasterdDataresource(
              this.form,
              "dataSrc",
              "masterdata"
            );
            this.formid = this.form.formId;
            this.form.components = this.form.components.filter(data => data.action !== 'submit' && data.key !== 'submit');
            this.themeObj = response?.theme ? response?.theme : {};
            this.setTheme(response.theme);
            this.isCard = response?.theme?.isCard;
          } else {
            this.showSpinner = false;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: "No form found" });
          }
        } else {
          this.showSpinner = false;
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
        }
      },
      error: (error) => {
        this.showSpinner = false;
        this.errmsg = error.error?.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  getPreviewFormData1() {  // ********************** NOT USING THIS FUNCTION *********************
    this.formsservice.getPreviewFormData(this.routerData.formId, this.routerData.type)
      .subscribe((response: any) => {
        this.dataPreview = response;
        // this.dataPreview = this.dataPreview.replace(/'/g, '\"');
        let baseUrl = window.location.href.replace(this.router.url, '');
        this.dataPreview = this.dataPreview.replace(/href='assets/g, `href='${baseUrl}/assets`);
        this.dataPreview = this.dataPreview.replace(/src='assets/g, `src='${baseUrl}/assets`);
        
        // if (!this.dataPreview.includes('name="viewport"')) {   // uncomment if needed, viewport is responsible to make the form responsive
        //       this.dataPreview = this.dataPreview.replace(
        //         /<head[^>]*>/i,
        //         match => `${match}
        // <script src="https://semantic-coolify:<EMAIL>/unvired-digital-forms/semantic-ui-template.git"></script>`
        //       );
        // }

        this.iframeElement = document.createElement('iframe');
        document.getElementById("render_form").appendChild(this.iframeElement);
        this.iframeElement.setAttribute("style","overflow-y:scroll; width: 100%; height: 100%;");
        this.iframeElement.setAttribute("frameBorder","0");
        const blob = new Blob([this.dataPreview], { type: 'text/html' });
         const url = URL.createObjectURL(blob);
         this.iframeElement.src = url;
        // this.iframeElement.onload = () => { // This code will add class stackable to make form responsive in mobile and tablet mode
        //   const iframeDoc = this.iframeElement.contentWindow?.document;
        //   if (iframeDoc) {
        //     const interval = setInterval(() => {
        //       const formElement = iframeDoc.querySelector('.ui.grid');
        //       if (formElement) {
        //         clearInterval(interval);
  
        //         const grids = iframeDoc.querySelectorAll('.ui.grid');
        //         grids.forEach((grid: HTMLElement) => {
        //           if (!grid.classList.contains('stackable')) {
        //             grid.classList.add('stackable');
        //           }
        //         });
        //       }
        //     }, 100);
        //   }
        // };
    });
  }

  getPreviewFormData() {  // Not calling API directly using form variable
    let baseUrl = window.location.href.replace(this.router.url, '');
    const formData = JSON.stringify(this.form);
    const htmlBody = `<html>
     <head>
        <style type=\"text/css\">
            ::-webkit-scrollbar {
                width: 0px;
                height: 0px;
            }

            ::-webkit-scrollbar-button {
                width: 0px;
                height: 0px;
            }

            ::-webkit-scrollbar-thumb {
                background: transparent;
                border: 0px none;
                border-radius: 0px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: transparent;
            }

            ::-webkit-scrollbar-thumb:active {
                background: transparent;
            }

            ::-webkit-scrollbar-track {
                background: transparent;
                border: 0px none;
                border-radius: 0px;
            }

            ::-webkit-scrollbar-track:hover {
                background: transparent;
            }

            ::-webkit-scrollbar-track:active {
                background: transparent;
            }

            ::-webkit-scrollbar-corner {
                background: transparent;
            }
        </style>
        <link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css'>
        <link rel='stylesheet' href='${baseUrl}/assets/js/semantic_new.min.css' />

        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/globals/site.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/elements/button.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/modules/checkbox.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/modules/tab.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/collections/menu.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/elements/segment.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/elements/header.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/elements/step.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/modules/dropdown.less' />
        <link rel='stylesheet/less' type='text/css' href='${baseUrl}/assets/fomantic-ui/definitions/collections/form.less' />
        
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/eventemitter3/5.0.1/index.min.js"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js"></script>
        <script>
          window.less = {
            async: true,
            environment: 'production',
            fileAsync: false,
            onReady: true,
            useFileCache: true
          };
        </script>
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/less@4.1.3/dist/less.min.js"></script>
        <script type='text/javascript'>
          window.onload = function () 
            { 
              
              Formio.use(semantic.default); 
              Formio.createForm(document.getElementById('formio'),${formData}); 

              const themeObj = ${JSON.stringify(this.themeObj)};
              var modifyVars = {};
              if (themeObj?.fontSize) modifyVars['@emSize'] = themeObj?.fontSize + 'px';
              if (themeObj?.labelColor) modifyVars['@labelColor'] = themeObj?.labelColor;
              if (themeObj?.answerColor) modifyVars['@inputColor'] = themeObj?.answerColor;
              if (themeObj?.inputFocusColor) modifyVars['@inputFocusColor'] = themeObj?.inputFocusColor;
              if (themeObj?.cardBackground && themeObj?.isCard) {
                modifyVars['@greyBackground'] = themeObj?.cardBackground
                const wrapper = document.getElementById('form-card-wrapper');
                if (wrapper) {
                  wrapper.style.backgroundColor = themeObj.cardBackground || '#ffffff';
                  wrapper.style.padding = '10px';
                  wrapper.style.borderRadius = '5px';
                }
              };
              if (themeObj?.backgroundColor) modifyVars['@pageBackground'] = themeObj?.backgroundColor;
              if (themeObj?.backgroundImage) {
                modifyVars['@uvFormBgImage'] = "url('" + themeObj.backgroundImage + "')";
              }
              if (themeObj?.fontFamily) {
                modifyVars['@fontName'] = themeObj?.fontFamily;
                WebFont.load({
                  google: {
                    families: [themeObj?.fontFamily],
                  },
                  active: function () {
                    document.body.style.fontFamily = themeObj?.fontFamily;
                  }
                });
              };
              if (Object.keys(modifyVars).length > 0) {
                less.modifyVars(modifyVars).then(() => {}).catch(err => {});
              }
            } 
        </script>
        <script type='text/javascript' src='https://cdn.form.io/formiojs/4.18.0/formio.full.min.js'></script>
        <script type='text/javascript' src='${baseUrl}/assets/js/semantic.js'></script>
        <script type='text/javascript' src='${baseUrl}/assets/custom-components/smart-id/smart-id.js'></script>
        <script type='text/javascript' src='${baseUrl}/assets/custom-components/barcode/barcode.js'></script>
        <script type='text/javascript' src='${baseUrl}/assets/custom-components/Location/location.js'></script>
        <script type='text/javascript' src='${baseUrl}/assets/custom-components/select-barcode/SelectBarcode.js'></script>
        <script type='text/javascript' src='${baseUrl}/assets/custom-components/nested-component/nested-component.js'></script>
        <script type='module' src='${baseUrl}/assets/custom-components/column/Column.js'></script>
      </head>

      <body>
        <div id="form-card-wrapper">
          <div class='formio-wrapper'>
              <div id="formio"></div>
            </div>
        </div>
      </body>

      </html>`;
      setTimeout(() => {
        this.iframeElement = document.createElement('iframe');
        document.getElementById("render_form").appendChild(this.iframeElement);
        this.iframeElement.setAttribute("style","overflow-y:scroll; width: 100%; height: 100%;");
        this.iframeElement.setAttribute("frameBorder","0");
        const blob = new Blob([htmlBody], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        this.iframeElement.src = url;
      }, 100);
  }

  getFormDataOnVersion(version: any) {
    this.showSpinner = true;
    if (version === 'Draft') {
      version = this.versionArr[1] + 1;   // if it is draft cannot pass string so latest version + 1
    }
    if (!this.routerData.iswftestdata) {
      this.formsservice
        .getFormVersionData(this.routerData.formId, this.routerData.type, version)
        .subscribe({ 
          next:(response) =>
          {
            if (response?.status?.toLowerCase() === 'success') {
              if (response?.formData) {
                this.showSpinner = false;
                this.form = response.formData;
                this.formid = response.formId;
                this.formTitle = response.formTitle;
                this.formVersionComment = response.versionComments;
                this.formVersionDate = response.updatedDate;
                this.releaseType = response?.releaseType;
                this.formVersion = response.version ? this.getVersionInfo(response.version, response?.isDraft) : null;
                // this.getAllTranslations();
                this.masterdataResources = response.masterdata;
                localStorage.setItem("resourceData",JSON.stringify(this.masterdataResources));
                this.form = this.getMasterdDataresource(
                  this.form,
                  "dataSrc",
                  "masterdata"
                );
                this.form.components = this.form.components.filter(data => data.action !== 'submit' && data.key !== 'submit');
                this.getLanguages(response.translations);
                this.themeObj = response?.theme ? response?.theme : {};
                this.setTheme(response.theme);
                this.isCard = response?.theme?.isCard;
              } else {
                this.showSpinner = false;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: "No form found" });
              }
            } else {
              this.showSpinner = false;
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
            }
          },
          error: (error) => {
            this.showSpinner = false;
            this.errmsg = error.error?.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
        });
    } else {
      this.showSpinner = false;
      this.masterdataResources = this.routerData.masterdata;
      this.form = this.getMasterdDataresource(
        this.routerData.form,
        "dataSrc",
        "masterdata"
      );
    }
  }

  getFlowTestData() {
    this.showSpinner = true;
    this.reteservice.getWfTestForm(this.routerData.formId, this.routerData.wfId).subscribe({ 
      next:(response) =>
      {
        if (response?.status?.toLowerCase() === 'success') {
          if (response?.formData) {
            this.showSpinner = false;
            this.routerData.form = response.formData ? response.formData : '';
            this.routerData.eventdata = response.eventData ? response.eventData : '';
            this.routerData.custominput = response.custominput ? response.custominput : '';
            this.submissiondata = response.testData && Object.keys(response.testData).length > 0 ? response.testData : null;
            this.routerData.masterdata = response.masterdata ? response.masterdata : '';
            this.masterdataResources = this.routerData.masterdata;
            this.form = this.getMasterdDataresource(
              this.routerData.form,
              "dataSrc",
              "masterdata"
            );
            this.formTitle = response.formTitle;
            this.formVersionComment = response.versionComments;
            this.formVersionDate = response.updatedDate;
            this.releaseType = response?.releaseType;
            this.formVersion = response.version ? this.getVersionInfo(response.version, response?.isDraft) : null;
            this.getLanguages(response?.translations);
            // this.lastUpdated = response.updatedDate;
            this.form.components = this.form.components.filter(data => data.action !== 'submit' && data.key !== 'submit');
            this.themeObj = response?.theme ? response?.theme : {};
            this.setTheme(response.theme);
            this.isCard = response?.theme?.isCard;
          } else {
            this.showSpinner = false;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: "No form found" });
          }
        } else {
          this.showSpinner = false;
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
        }
      },
      error: (error) => {
        this.showSpinner = false;
        this.errmsg = error.error?.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  getShaerdSubmissionData() {
    this.showSpinner = true;
    this.formsservice.getsharedformdata(this.routerData.shareId, this.routerData?.submissionId)
    .subscribe({ 
      next:(response) =>
      {
        if (response?.status?.toLowerCase() === 'success') {
          if (response?.form) {
            this.showSpinner = false;
            this.readonly = true; // check the flage for private and public
            this.form = response.form;
            this.submissiondata = response.submissionData && Object.keys(response.submissionData).length > 0 ? response.submissionData : null;
            this.formTitle = response.formTitle;
            this.formVersionComment = response.formVersionComments;
            this.formVersionDate = response.updatedDate;
            this.releaseType = response?.releaseType;
            this.formVersion = response.formVersion ? this.getVersionInfo(response.formVersion, response?.isDraft) : null;
            this.form.components = this.form.components.filter(data => data.action !== 'submit' && data.key !== 'submit');
            this.getLanguages(response?.translation);
            this.themeObj = response?.theme ? response?.theme : {};
            this.setTheme(response.theme);
            this.isCard = response?.theme?.isCard;
          } else {
            this.showSpinner = false;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: "No form found" });
          }
        } else {
          this.showSpinner = false;
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
        }
      },
      error: (error) => {
        this.showSpinner = false;
        this.errmsg = error.error?.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  getReportSubmissionData() {
    this.showSpinner = true;
    this.getEditManagerPermission();
    this.formsservice.getresourceformdata(this.routerData).subscribe({ 
      next:(response) =>
      {
        if (response?.status?.toLowerCase() === 'success') {
          this.showSpinner = false;
          this.setFormRenderer(response);
          this.formTitle = this.routerData.formTitle;
          this.formVersionComment = response.formVersionComments;
          this.formVersionDate = response.updatedDate;
          this.releaseType = response?.releaseType;
          this.formVersion = response.formVersion ? this.getVersionInfo(response.formVersion, response?.isDraft) : null;
          this.lastUpdated = this.routerData.submittedOn;
          this.lastUpdatedBy = this.routerData.submittedBy;
          this.getLanguages(response?.translations);
        } else {
          this.showSpinner = false;
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
        }
      },
      error: (error) => {
        this.showSpinner = false;
        this.errmsg = error.error?.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  getAuditlogDetails() {
    this.showSpinner = true;
    this.getEditManagerPermission();
    this.reportService.getAuditLogDetails(this.routerData.auditId).subscribe({ 
      next:(response) =>
      {
        if (response?.status?.toLowerCase() === 'success') {
          this.showSpinner = false;
          this.setFormRenderer(response);
          this.formTitle = this.routerData.formTitle;
          this.formVersionComment = response.versionComments;
          this.formVersionDate = response.updatedTime;
          this.releaseType = response?.releaseType;
          this.formVersion = response.formVersion ? this.getVersionInfo(response.formVersion, response?.isDraft) : null;
          this.getLanguages(response?.translations);
        } else {
          this.showSpinner = false;
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
        }
      },
      error: (error) => {
        this.showSpinner = false;
        this.errmsg = error.error?.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  onFormReady() {
    setTimeout(() => {
      let eventCustom = new CustomEvent('FormRenderingComplete', {});
      document.dispatchEvent(eventCustom);
      console.log('Form is ready to render');
    }, 1000);
  }

  getLanguages(data: any) {
    let languageKey: any;
    let nativeLanguage: any;
    if (data) {
      this.translationLanguages = data;
      this.httpClient.get("assets/i18n/all.json").subscribe((data) => {
        this.i18nAll = data;
        this.languages = [
          {
            key: localStorage.getItem('language'),
            display: "Default Language",
          },
        ];
        for (let key in this.translationLanguages) {
          languageKey = key;
          for (let i18nKey in this.i18nAll) {
            if (i18nKey === languageKey) {
              languageKey = i18nKey;
              nativeLanguage = this.i18nAll[i18nKey].nativeName;
            }
          }
          this.languages.push({ key: key, display: nativeLanguage });
        }
      });
    } else {
      this.translationLanguages = [];
    }
  }

  // setting masterdata resource
  public getMasterdDataresource(obj: any, key: any, val: any) {
    let objects = [];
    for (let i in obj) {
      if (typeof obj[i] === "object") {
        objects = objects.concat(this.getMasterdDataresource(obj[i], key, val));
      } else if (i === key && obj[key] === val) {
        // let val1 = obj['valueProperty'];
        let ress;
        if (
          obj.data &&
          typeof obj.data === "object" &&
          obj.data !== undefined
        ) {
          if (obj.data["masterdata"]) {
            ress = obj.data["masterdata"];
          }
        }
        let obj1 = [];

        const resource = this.masterdataResources;
        if (
          resource !== undefined &&
          resource !== "" &&
          ress !== undefined &&
          ress !== ""
        ) {
          for (let x = 0; x < resource.length; x++) {
            const s = resource[x].resourceName;
            if (s === ress) {
              obj1.push(JSON.parse(resource[x].data).data);
            }
          }
        } else {
          // console.log("resource is empty");
          obj1 = [];
        }
        const tmplt = obj["template"];
        let str2 = "";
        if (tmplt !== "") {
          const str1 = tmplt.substring(
            tmplt.lastIndexOf(".") + 1,
            tmplt.length
          );
          str2 = str1.substring(0, str1.indexOf("}"), str1.length);
          const str3 = "<span>{{" + "item." + str2.trim() + "}}</span>";
          obj["template"] = str3;
        }
        // tslint:disable-next-line:no-shadowed-variable
        const val = obj["valueProperty"];
        let valProp = "";
        if (val !== "") {
          valProp = val.substring(val.lastIndexOf(".") + 1, val.length);
          obj["valueProperty"] = valProp;
        }
        str2 = str2.trim();
        obj1 = obj1.sort(function (a, b) {
          return a[str2] > b[str2] ? 1 : a[str2] < b[str2] ? -1 : 0;
        });
        obj.masterdata = obj1;
      }
    }
    return obj;
  }

  onSubmit(event: any) {
    this.showSpinner = true;
    this.isSubmitting = true;
    if (this.routerData.iswftestdata) {
      this.reteservice.saveWfTestForm(this.routerData.formId,event.data,this.routerData.eventdata,this.routerData.wfId,this.routerData.custominput,this.formUserData).subscribe({ 
        next:(response) =>
        {
          if (response?.status?.toLowerCase() === 'success') {
            this.showSpinner = false;
            this.isSubmitting = false;
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Test Data added successfully' });
            window.opener.postMessage(this.routerData.message, '*');
            setTimeout(() => {
              window.close();
            }, 1000)
          } else {
            this.showSpinner = false;
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
          }
        },
        error: (error) => {
          this.showSpinner = false;
          this.errmsg = error.error?.error;
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        }
      });
    } else if (this.routerData.submissionId) {
      this.formsservice.updatesubmissiondata(this.routerData.name, this.routerData.type, this.routerData.submissionId, event).subscribe({ 
        next:(response) =>
        {
          if (response?.status?.toLowerCase() === 'success') {
            this.showSpinner = false;
            this.isSubmitting = false;
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Test Data added successfully' });
            setTimeout(() => {
              window.close();
            }, 1000)
          } else {
            this.showSpinner = false;
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
          }
        },
        error: (error) => {
          this.showSpinner = false;
          this.errmsg = error.error?.error;
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        }
      });
    }
  }
  // savewftestform() {
  //   this.reteservice.saveWfTestForm(this.data.formId, this.form)
  //   .subscribe(res => {
  //     const response = JSON.parse(res);
  //     if (response.error === '') {
  //       if (response.status === 'Success') {
  //         console.log('data saved');
  //     } else {
  //       this.errmsg = response.error;
  //     }
  //   }});
  // }
  // closeoverlay(data:any) {
  //   this.dialogRef.close(data);
  // }
  handleCustomEvent(formioCustomEvent: any) {
    // console.log(formioCustomEvent, "handleCustomEvent called");
    // console.log('Custom Event fired', formioCustomEvent);
    if (
      formioCustomEvent.type &&
      formioCustomEvent.type !== "WorkflowExecuteError"
    ) {
      // TO DO: Add fuctionality on Event
      this.refreshForm.emit({
        // form: this.form,
        submission: {
          data: formioCustomEvent.data,
        },
      });
    }
    // else if (formioCustomEvent.type === 'callWF') {
    // console.log(formioCustomEvent);
    // let eventCustom = new CustomEvent(formioCustomEvent.type, {detail:formioCustomEvent.data});
    // document.dispatchEvent(eventCustom);
    // if (CustomEvent.component.barcodeDataField) {
    //   const keys = CustomEvent.component.key;
    //   this.refreshForm.emit({
    //    // form: this.form,
    //       submission: {
    //         data: {
    //           [keys] : CustomEvent.data
    //         }
    //       }
    //   });
    // }
    // }
    else if (formioCustomEvent.type === "WorkflowExecuteError") {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: `${formioCustomEvent.data.error}` });
    }
  }

  /**--- open the dialog after catching the scanner event */
  openSimulateScanDialog(componentObj: any) {
    const ref = this.dialogService.open(BarcodeDialogComponent, {
      header: 'Simulate Barcode Scan',
      width: '50%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        barcodedata: componentObj.getValue()
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        const eventCustom = new CustomEvent("scannedData", {
          detail: data,
        });
        document.dispatchEvent(eventCustom);
      }
    });
  }

  public getAllTranslations() {
    const array = [];
    let key: any;
    for (key in this.translationLanguages) {
      if (key == this.currentLanguage) {
        this.seletedLanguage = "";
        this.seletedLanguage = {
          language: key,
          i18n: {
            [key]: this.translationLanguages[key],
          },
        };
      }
      array.push(key);
    }
    // when come from Flow Test data
    if (this.routerData.wfId) {
      this.getFlowTestData();
    }
    // when come from Share Dialog
    if (this.routerData?.shareId) {
      this.getShaerdSubmissionData();
    }
    // when come from Version Dialog
    if (this.routerData?.version) {
      this.getFormDataOnVersion(this.routerData.version);
      this.versionArr = this.routerData?.versionArr;
      this.currentVersion = this.routerData?.version;
    }
    // when come from MasterData
    if (this.routerData.type === 'masterdata') {
      this.getDataOfMasterData();
    }
    // when come from Reports
    if (this.routerData?.isReport) {
      if (this.routerData.submissionId) { 
        this.getReportSubmissionData();
      } else if (this.routerData?.auditId) {
        this.getAuditlogDetails();
      }
    }
    // when come from form-builder
    if (this.routerData.isPreview) {
      this.getFormData();
    }
    if (array.includes(this.currentLanguage)) {
    } else {
      this.seletedLanguage = "";
      this.getFormData();
    }
  }

  selectDevice(device: string) {
    this.getPreviewFormData();
    this.device = device;
    this.mode = 'portrait';
    this.isCustomDevice = false;
    setTimeout(() => {     // <-- method call after 'id' will set in view
      if (this.device === 'Mobile') {
        this.hideDeviceIcons = true;
        document.getElementById("divSize").style.height = "789px";
        document.getElementById("divSize").style.width = "403px";
        this.customHeight = 674;
        this.customWidth = 377;
      } else if (this.device === 'Tablet') {
        this.hideDeviceIcons = true;
        document.getElementById("divSize").style.height = "1149px";
        document.getElementById("divSize").style.width = "796px";
        this.customHeight = 1031;
        this.customWidth = 770;
      } else if (this.device === 'Desktop') {
        document.getElementById("divSize").style.height = "100%";
        document.getElementById("divSize").style.width = "100%";
        this.customHeight = null;
        this.customWidth = null;
      }
    }, 0);
  }

  landscapePotraitMode(mode: string) {
    this.mode = mode;
    // this.isLandscape = !this.isLandscape;
    if (mode === 'portrait') {
      if (this.device === 'Mobile') {
        document.getElementById("divSize").style.height = "789px"; // this will be 789px then it load 740px
        document.getElementById("divSize").style.width = "403px"; // this will be 403px then it load 400px
        this.customHeight = 674; // this will be 674px then it load 667px
        this.customWidth = 377; // this will be 377px then it load 374px
      } else if (this.device === 'Tablet') {
        document.getElementById("divSize").style.height = "1149px";
        document.getElementById("divSize").style.width = "796px";
        this.customHeight = 1031; // 1024px
        this.customWidth = 770; // 768px
      } else if (this.device === 'Desktop') {
        document.getElementById("divSize").style.height = "100%";
        document.getElementById("divSize").style.width = "100%";
        this.customHeight = null;
        this.customWidth = null;
      }
    } else if (mode === 'landscape') {
      if (this.device === 'Mobile') {
        document.getElementById("divSize").style.height = "408px";
        document.getElementById("divSize").style.width = "784px";
        this.customHeight = 382;
        this.customWidth = 669;
      } else if (this.device === 'Tablet') {
        document.getElementById("divSize").style.height = "801px";
        document.getElementById("divSize").style.width = "1144px";
        this.customHeight = 775;
        this.customWidth = 1026;
      } else {
        document.getElementById("divSize").style.height = "100%";
        document.getElementById("divSize").style.width = "100%";
        this.customHeight = null;
        this.customWidth = null;
      }
    }
  }

  showCustomDevice() {
    this.getPreviewFormData();
    this.hideDeviceIcons = true;
    this.isCustomDevice = true;
    this.device = 'Custom';
    this.customHeight = null;
    this.customWidth = null;
    setTimeout(() => {
      this.customDevice();
    }, 300);
  }

  customDevice() {
    document.getElementById("divSize").style.height = this.customHeight ? this.customHeight + "px" : '100%';
    document.getElementById("divSize").style.width = this.customWidth ? this.customWidth + "px" : '100%';
    // document.getElementById("divSize").style.margin = '20px auto';
  }

  goBackToDeviceIcons() {
    this.hideDeviceIcons = false;
    this.device = 'Desktop';
    this.isCustomDevice = false;
  }

  goBack() {
    this.location.back();
  }

  // Masterdata start**********************

  getDataOfMasterData() {
    this.showSpinner = true;
    this.formsservice.getresourceformdata(this.routerData).subscribe({ 
      next:(response) =>
      {
        if (response?.status?.toLowerCase() === 'success') {
          this.showSpinner = false;
          this.setFormRenderer(response);
        } else {
          this.showSpinner = false;
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
        }
      },
      error: (error) => {
        this.showSpinner = false;
        this.errmsg = error.error?.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  setFormRenderer(formData: any) {
    this.masterdataResources = formData.masterdata;
    localStorage.setItem('resourceData', JSON.stringify(this.masterdataResources));
    const resdata = formData.template;
    if (!formData.managerCanEdit || this.routerData?.status?.toLowerCase() == 'completed') {
      this.readonly = true;
    }
    let formwithselectlistdata = this.getObjects(resdata,'dataSrc','json');
    formwithselectlistdata = this.getMasterdDataresource(resdata, 'dataSrc','masterdata');
    if (formwithselectlistdata !== undefined && formwithselectlistdata !== '') {
      this.form = formwithselectlistdata;
      this.form.components = this.form.components.filter(data => data.action !== 'submit' && data.key !== 'submit');
      this.themeObj = formData?.theme ? formData?.theme : {};
      this.setTheme(formData.theme);
      this.isCard = formData?.theme?.isCard;
    } else {
      this.form = formData.template;
      this.form.components = this.form.components.filter(data => data.action !== 'submit' && data.key !== 'submit');
      this.themeObj = formData?.theme ? formData?.theme : {};
      this.setTheme(formData.theme);
      this.isCard = formData?.theme?.isCard;
    }
    // this.resourcedata.nosubmit = true;
    if (formData.submissionData?.data) {
      formData.submissionData.data.submit = false;
    }
    this.submissiondata = formData?.submissionData.data && Object.keys(formData?.submissionData.data).length > 0 ? formData?.submissionData.data : null;
  }

  public getObjects(obj, key, val) {
    let objects = [];
    for (const i in obj) {
      if (typeof obj[i] === 'object') {
        objects = objects.concat(this.getObjects(obj[i], key, val));
      } else if (i === key && obj[key] === val) {
        const val1 = obj['valueProperty'];
        const val2 = val1.indexOf('.');
        const ress = val1.slice(0, val2);
        const obj1 = [];
        const resource = this.masterdataResources;
        if (resource !== undefined && resource !== '') {
          for (let x = 0; x < resource.length; x++) {
            const s = resource[x].resourceName;
            if (s === ress) {
              obj1.push(JSON.parse(resource[x].data));
            }
          }
        }
        if (obj.data && typeof obj.data === 'object' && obj.data !== undefined) {
          if (obj.data['json'] === '') {
            obj.data['json'] = obj1;
            const tmplt = obj['template'];
            if (tmplt !== '') {
              const str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
              const str2 = str1.substring(0, str1.indexOf(' '), str1.length);
              const str3 = '<span>{{ ' + 'item.' + str2 + '}}</span>';
              obj['template'] = str3;
            }
            const value = obj['valueProperty'];
            if (value !== '') {
              const valProp = value.substring(value.lastIndexOf('.') + 1, value.length);
              obj['valueProperty'] = valProp;
            }
          } else {
            obj.data['json'] = obj.data['json'];
          }
        } else if (obj.data === undefined) {
          obj['data'] = {
            'values': [],
            'json': '',
            'url': '',
            'resource': '',
            'custom': ''
          };
          obj.data['json'] = obj1;
          const tmplt = obj['template'];
          if (tmplt !== '') {
            const str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
            const str2 = str1.substring(0, str1.indexOf(' '), str1.length);
            const str3 = '<span>{{ ' + 'item.' + str2 + '}}</span>';
            obj['template'] = str3;
          }
          const value = obj['valueProperty'];
          if (value !== '') {
            const valProp = value.substring(value.lastIndexOf('.') + 1, value.length);
            obj['valueProperty'] = valProp;
          }
        }
      }
    }
    return obj;
  }

  getEditManagerPermission() {
    this.settingservice.getgeneralsettings().subscribe(data => {
      if (this.routerData?.status?.toLowerCase() == 'completed') {
        this.readonly = true;
      } else {
        if (data.general.generalsettingsform.Allow_managers_to_edit_submissions == true && localStorage.getItem('Role').toLowerCase() == 'manager') {
          this.readonly = false;
        } else if(localStorage.getItem('Role').toLowerCase() == 'admin') {
          this.readonly = false;
        } else {
          this.readonly = true;
        }
      }
    });
  }

  submitData() {
    this.formioComponent.formio.submitForm()
    .then((response) => {
      if (response) {
        if (response?.submission?.data) {
          this.onSubmit(response.submission)
        } else {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Fill all mandatory fields.' });
        }
      }
    })
    .catch((error) => {
      // console.error('Error submitting form  ', error);
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Fill all mandatory fields.' });
    });
    // if (this.formioComponent?.formio?.submission) {
    //   this.onSubmit(this.formioComponent?.formio?.submission)
    // }
  }

  downloadForm() {
    const data = {
      "formId": this.formid,
      "version": this.formVersion ? Number(this.formVersion) : null,
    }
    this.formsservice.generateFormPdf(data).subscribe((res) => {
      if (res.status.toLowerCase() === 'success') {
        this.formsservice.downloadFile(res.pdfId).subscribe((resp)=>{
          const a = document.createElement('a');
          a.setAttribute('type', 'hidden');
          a.href = URL.createObjectURL(resp);
          a.download = `${this.formTitle}`+"-"+`${this.formVersion}` + '.pdf';
          a.click();
          a.remove();
        });
      } else {
        this.errmsg = res.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  getVersionInfo(version: number, isdraft: boolean): string {
    if (this.landsacpe?.toLowerCase() === 'production') return isdraft ? 'Draft' : version?.toString();
    if (this.releaseType === 'preRelease') {
      return `${version} (Pre Release)`;
    } else if (this.releaseType === 'finalRelease') {
      return `${version}`;
    } else {
      return 'Draft';
    }
  }

  setTheme(theme: any) {
    if (theme) {
      const themeObj = theme;
      var modifyVars = {

      // global font size and font name variables
      //'@emSize': themeObj?.fontSize + 'px', // for global em size
      //'@fontName': themeObj?.fontName, // for global font name

      // common theme variables
      //'@primaryColor  ': 'green', // for primary color
      // '@pageBackground': themeObj?.backgroundColor, // for body background color
      // '@labelColor': themeObj?.labelColor, // for label colors
      // '@inputColor': themeObj?.answerColor, // for input value colors
      // '@inputFocusColor': themeObj?.inputFocusColor, // for input value colors on focus
      // '@greyBackground': themeObj?.cardBackground, // for card background color
      // '@solidBorderColor': 'blue', // for primary border color

      // page image and color variables
      //'@uvFormBgImage': `url("${themeObj?.backgroundImage}")`, // for body image

      // wizard variables
      //'@stepsBackground': '#909090', // for wizard background color
      /*
      ** Wizard optional variables
      '@stepHeaderContentDisplay': 'none', // for wizard header display optional
      '@wizardHeaderTitleColor': 'green', // for wizard header title and description color
      '@stepsBorderColor': 'blue', // for wizard step icon border background color
      '@stepTextColor': 'green', // for wizard step default color
      '@stepActiveColor': 'yellow', // for wizard step Active color
      **
      */
      };

      if (themeObj?.fontSize) {
        modifyVars['@emSize'] = themeObj.fontSize + 'px';
      }
      if (themeObj?.fontFamily) {
        modifyVars['@fontName'] = themeObj.fontFamily;
        this.el.nativeElement.style.setProperty('--fontName', themeObj.fontFamily);
        WebFont.load({
              google: {
                families: [themeObj.fontFamily],
              },
            });
      }
      if (themeObj?.backgroundColor) {
        modifyVars['@pageBackground'] = themeObj.backgroundColor;
      }
      if (themeObj?.labelColor) {
        modifyVars['@labelColor'] = themeObj.labelColor;
      }
      if (themeObj?.answerColor) {
        modifyVars['@inputColor'] = themeObj.answerColor;
      }
      if (themeObj?.inputFocusColor) {
        modifyVars['@inputFocusColor'] = themeObj.inputFocusColor;
      }
      if (themeObj?.cardBackground) {
        modifyVars['@greyBackground'] = themeObj.cardBackground;
      }
      if (themeObj?.backgroundImage) {
        modifyVars['@uvFormBgImage'] = `url("${themeObj?.backgroundImage}")`;
      }
      if (themeObj?.isCard && themeObj?.cardBackground) {
        this.el.nativeElement.style.setProperty('--cardBbackground', themeObj?.cardBackground);
      }
      if (themeObj?.isHeader && themeObj?.headerBackground) {
        this.el.nativeElement.style.setProperty('--headerBackground', themeObj?.headerBackground);
      } else {
        this.el.nativeElement.style.setProperty('--headerBackground', 'var(--surface-a)');
      }

      less.modifyVars(modifyVars).then(() => {}).catch((error: any) => {});
    //     const themeObj = JSON.parse(theme);
    //     this.el.nativeElement.style.setProperty('--emSize', themeObj?.fontSize + 'px');
    //     this.el.nativeElement.style.setProperty('--fontName', 'Playball');
    //     // this.el.nativeElement.style.setProperty('--fontName', themeObj?.fontName);
    //     this.el.nativeElement.style.setProperty('--labelColor', themeObj?.labelColor);
    //     this.el.nativeElement.style.setProperty('--inputFocusColor', '#ff00cd');
    //     this.el.nativeElement.style.setProperty('--pageBackground', themeObj?.backgroundColor);
        
    } else {
      this.el.nativeElement.style.setProperty('--headerBackground', 'var(--surface-a)');
    }
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    this.linkRefs.forEach(link => {
      document.head.removeChild(link);
    });
    delete (window as any).less;
  }

}
