.bordar {
    border: 1px solid #dee2e6!important;
    border-radius: 5px;
  }
  .main-title {
    font-size: 14px;
    font-weight: 700;
  }
  .description {
    font-size: 14px;
    font-weight: 400;
  }
  .share-list {
    border: none !important;
  }
  .share-list:hover {
    background-color: #cdd5e44f;
    border-radius: 10px;
    cursor: pointer;
  }
  ::ng-deep .share p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 10px 15px 10px 15px !important;
  }
  ::ng-deep .cat p-multiselect .p-multiselect {
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
  }
  
  ::ng-deep .cat p-multiselect .p-multiselect-label {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    max-height: 40px;
  }
  
  ::ng-deep .cat p-multiselect .p-multiselect-token{
    margin-bottom: .5rem;
  }
  
  ::ng-deep .cust-button p-selectbutton .p-button {
    font-size: 0.875rem !important;
    padding: 0.65625rem 1.09375rem !important;
  }
  
  .no-data {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  .active {
    color: green;
  }
  
  .in-active {
    color: red;
  }
  
  .copy-icon:hover {
    color: #2586c7;
  }

  .copy-url:hover {
    color: var(--primary-color);
    transform: scale(1.01, 1.01);
  }
  .help-icon {
    position: absolute;
    top: 22px;
    right: 55px;
  }
  ::ng-deep .external p-fieldset .p-fieldset-legend {
    padding: 10px !important;
    font-weight: normal !important;
  }