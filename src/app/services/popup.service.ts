import { Injectable, Injector } from '@angular/core';
import { ConfigService } from './config.service';
import { AuthenticationService } from './authentication.service';

@Injectable({
  providedIn: 'root'
})
export class PopupService {
  public application = 'DIGITAL_FORMS';
  public redirecturl = this.getRedirectUrlPath();
  public device = 'browser';
  public popupurl: string;
  public popupOptions = {
    width: 600,
    height: 600,
    top: window.screenY + ((window.outerHeight - 600) / 2.5),
    left: window.screenX + ((window.outerWidth - 600) / 2)
  };
  constructor(private injector: Injector) { }
  openSSO (popuptype: string, company: string = null): Promise<any> {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';
   if (popuptype === 'login') {
    if (!company) {
      company = localStorage.getItem('domain');
    }
    this.popupurl = `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
   } else if (popuptype === 'logout') {
    const token = localStorage.getItem('token');
    this.popupurl = `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;

   } else {
     console.log('SSO type missing');
     return Promise.reject(new Error('Invalid SSO type'));
   }

    try {
      // Clear any previous SSO result
      localStorage.removeItem('sso_result');

      // Open SSO in new tab instead of popup
      const ssoTab = window.open(this.popupurl, '_blank');
      if (!ssoTab) {
        return Promise.reject(new Error('Failed to open SSO tab - please allow popups for this site'));
      }

      return new Promise((resolve, reject) => {
        // Listen for storage events from the SSO tab
        const handleStorageChange = (e: StorageEvent) => {
          if (e.key === 'sso_result') {
            window.removeEventListener('storage', handleStorageChange);

            const result = JSON.parse(e.newValue || '{}');
            localStorage.removeItem('sso_result');

            if (result.success) {
              if (popuptype === 'login') {
                // Call authentication service to properly store all user details
                const authService = this.injector.get(AuthenticationService);
                const jwtres = { token: result.token };
                const user = { domain: company };
                authService.storeTokenAndlogIn(jwtres, user);
                // Override login_type to SSO after storeTokenAndlogIn
                localStorage.setItem('login_type', 'SSO');
                window.location.reload();
              } else {
                // For logout, preserve domain but remove other auth data
                const preservedDomain = localStorage.getItem('domain');
                localStorage.removeItem('token');
                localStorage.removeItem('login_type');
                localStorage.removeItem('name');
                localStorage.removeItem('Role');
                localStorage.removeItem('landscape');
                localStorage.removeItem('email');
                localStorage.removeItem('UMP_url');
                // Restore domain for future SSO logins
                if (preservedDomain) {
                  localStorage.setItem('domain', preservedDomain);
                }
                window.location.reload();
              }
              resolve({ success: true });
            } else {
              reject(new Error(result.error || 'SSO authentication failed'));
            }
          }
        };

        // Listen for storage changes
        window.addEventListener('storage', handleStorageChange);

        // Also listen for focus events as backup
        const handleFocus = () => {
          // Check if SSO result was set
          const ssoResult = localStorage.getItem('sso_result');
          if (ssoResult) {
            window.removeEventListener('focus', handleFocus);
            handleStorageChange({ key: 'sso_result', newValue: ssoResult } as StorageEvent);
          }
        };

        window.addEventListener('focus', handleFocus);

        // Timeout after 5 minutes
        setTimeout(() => {
          window.removeEventListener('storage', handleStorageChange);
          window.removeEventListener('focus', handleFocus);
          reject(new Error('SSO authentication timeout - please try again'));
        }, 300000);
      });

    } catch (e) {
      console.log('Error opening SSO tab:', e);
      return Promise.reject(new Error('Failed to open SSO tab'));
    }
  }

  /*
  getDevice() {
    const userAgent = navigator.userAgent || navigator.vendor;
      // Windows Phone must come first because its UA also contains "Android"
      if (/windows phone/i.test(userAgent)) {
          return 'windows';
      }
      if (/android/i.test(userAgent)) {
          return 'android';
      }
      if (/iPad/i.test(userAgent) && !window.MSStream) {
          return 'ipad';
      }
      if (/iPhone/.test(userAgent) && !window.MSStream) {
        return 'iphone';
      }
      return 'browser';
  }
  */
 
  getRedirectUrlPath() {
    const origin = location.origin; // includes protocol + hostname + port (if any)
    const pathname = location.pathname; // includes the base href path like /app

    // Extract base path (everything except the current route)
    const basePath = pathname.substring(0, pathname.lastIndexOf('/')) || '';

    // Construct full redirect URL to our SSO redirect handler
    return origin + basePath + '/sso-redirect.html';
  }

  getLoginUrl(popuptype: string, company: string = null): string {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';

    if (popuptype === 'login') {
      if (!company) {
        company = localStorage.getItem('domain');
      }
      return `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
    } else if (popuptype === 'logout') {
      const token = localStorage.getItem('token');
      return `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;
    } else {
      return 'Invalid popup type';
    }
  }

  _stringifyOptions() {
    const options = [];
    for (const optionKey in this.popupOptions) {
      if (!this.isUndefined(this.popupOptions[optionKey])) {
        options.push(`${optionKey}=${this.popupOptions[optionKey]}`);
      }
    }
    return options.join(',');
  }
  isUndefined(value: any) {
    return typeof value === 'undefined';
  }


}
