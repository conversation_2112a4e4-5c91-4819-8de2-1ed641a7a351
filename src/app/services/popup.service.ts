import { Injectable, Injector } from '@angular/core';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class PopupService {
  public application = 'DIGITAL_FORMS';
  public redirecturl = this.getRedirectUrlPath();
  public device = 'browser';
  public popupurl;
  public popupOptions = {
    width: 600,
    height: 600,
    top: window.screenY + ((window.outerHeight - 600) / 2.5),
    left: window.screenX + ((window.outerWidth - 600) / 2)
  };
  constructor(private injector: Injector) { }
  openPopup (popuptype: string, company: string = null): Promise<any> {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';
   if (popuptype === 'login') {
    if (!company) {
      company = localStorage.getItem('domain');
    }
    this.popupurl = `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
   } else if (popuptype === 'logout') {
    const token = localStorage.getItem('token');
    this.popupurl = `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;

   } else {
     console.log('popup type missing');
     return Promise.reject(new Error('Invalid popup type'));
   }
    try {
      const popup = window.open(this.popupurl, '_blank', this._stringifyOptions());
      if (popup && popup.focus) {
        popup.focus();
      }
      if (this.popupurl === 'about:blank') {
        popup.document.body.innerHTML = 'Loading...';
      }
      return this.pollPopup(popup, popuptype, company).then(
        (params: URLSearchParams) => {
            if (popuptype === 'login') {
              const token = params.get('token');
              localStorage.setItem('token', token);
              localStorage.setItem('login_type', 'SSO');
              // Save the domain used for SSO login
              if (company) {
                localStorage.setItem('domain', company);
              }
              window.location.reload();
            } else {
              localStorage.removeItem('token');
              localStorage.removeItem('login_type');
              window.location.reload();
            }
            return { success: true };
        }
        ).catch(
          error => {
            console.log('SSO Popup Error:', error);
            // Return error to be handled by calling component
            return { success: false, error: error.message };
          }
        );

    } catch (e) {
      console.log('Error opening popup:', e);
      return Promise.reject(new Error('Failed to open popup window'));
    }
  }
  pollPopup(window, popuptype, company = null) {
    return new Promise((resolve, reject) => {
        const polling = setInterval(() => {
        if (!window || window.closed || window.closed === undefined) {
          clearInterval(polling);
          reject(new Error('The popup window was closed'));
        }
        try {
          if (popuptype === 'logout'){
            localStorage.removeItem('token');
            localStorage.removeItem('login_type');
          }
          const popupUrlPath = window.location.protocol + '//' + window.location.host + window.location.pathname;
          if (popupUrlPath === this.redirecturl) {
            if (window.location.search) {
              const urlParams = new URLSearchParams(window.location.search);
              const error = urlParams.get('error');
              if (error) {
                // Close popup and return error info to main app
                clearInterval(polling);
                window.close();
                reject(new Error(`SSO Error: ${error} - ${urlParams.get('error_description') || ''}`));
              } else {
                resolve(urlParams);
                clearInterval(polling);
                window.close();
              }
            } else {
              reject(new Error('OAuth redirect has occurred but no query parameters were found.'));
            }
          }
        } catch (error) {
            // Ignore DOMException: Blocked a frame with origin from accessing a cross-origin frame.
            // A hack to get around same-origin security policy errors in Internet Explorer.
        }
      }, 500);
    });
  }
  /*
  getDevice() {
    const userAgent = navigator.userAgent || navigator.vendor;
      // Windows Phone must come first because its UA also contains "Android"
      if (/windows phone/i.test(userAgent)) {
          return 'windows';
      }
      if (/android/i.test(userAgent)) {
          return 'android';
      }
      if (/iPad/i.test(userAgent) && !window.MSStream) {
          return 'ipad';
      }
      if (/iPhone/.test(userAgent) && !window.MSStream) {
        return 'iphone';
      }
      return 'browser';
  }
  */
 
  getRedirectUrlPath() {
    const origin = location.origin; // includes protocol + hostname + port (if any)
    const pathname = location.pathname; // includes the base href path like /app

    // Extract base path (everythiing except the current route)
    const basePath = pathname.substring(0, pathname.lastIndexOf('/')) || '';

    // Construct full redirect URL respecting deployment path
    return origin + basePath + '/login';
  }

  getLoginUrl(popuptype: string, company: string = null): string {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';

    if (popuptype === 'login') {
      if (!company) {
        company = localStorage.getItem('domain');
      }
      return `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
    } else if (popuptype === 'logout') {
      const token = localStorage.getItem('token');
      return `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;
    } else {
      return 'Invalid popup type';
    }
  }

  _stringifyOptions() {
    const options = [];
    for (const optionKey in this.popupOptions) {
      if (!this.isUndefined(this.popupOptions[optionKey])) {
        options.push(`${optionKey}=${this.popupOptions[optionKey]}`);
      }
    }
    return options.join(',');
  }
  isUndefined(value: any) {
    return typeof value === 'undefined';
  }


}
