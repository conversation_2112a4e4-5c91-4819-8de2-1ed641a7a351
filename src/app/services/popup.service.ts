import { Injectable, Injector } from '@angular/core';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class PopupService {
  public application = 'DIGITAL_FORMS';
  public redirecturl = this.getRedirectUrlPath();
  public device = 'browser';
  public popupurl;
  public popupOptions = {
    width: 600,
    height: 600,
    top: window.screenY + ((window.outerHeight - 600) / 2.5),
    left: window.screenX + ((window.outerWidth - 600) / 2)
  };
  constructor(private injector: Injector) { }
  openPopup (popuptype: string, company: string = null) {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';
   if (popuptype === 'login') {
    if (!company) {
      company = localStorage.getItem('domain');
    }
    this.popupurl = `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
   } else if (popuptype === 'logout') {
    const token = localStorage.getItem('token');
    this.popupurl = `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;

   } else {
     console.log('popup type missing');
   }
    try {
      const popup = window.open(this.popupurl, '_blank', this._stringifyOptions());
      if (popup && popup.focus) {
        popup.focus();
      }
      if (this.popupurl === 'about:blank') {
        popup.document.body.innerHTML = 'Loading...';
      }
      this.pollPopup(popup, popuptype, company).then(
        (params: URLSearchParams) => {
            if (popuptype === 'login') {
              const token = params.get('token');
              localStorage.setItem('token', token);
              localStorage.setItem('login_type', 'SSO');
              // Save the domain used for SSO login
              if (company) {
                localStorage.setItem('domain', company);
              }
              window.location.reload();
            } else {
              localStorage.removeItem('token');
              localStorage.removeItem('login_type');
              window.location.reload();
            }
        }
        ).catch(
          error => {
            console.log('SSO Popup Error:', error);
            // Error is already displayed in popup, no need to navigate or reload
          }
        );

    } catch (e) {
      console.log('Error opening popup:', e);
      // return throwError(new Error('OAuth popup error occurred'))
    }
  }
  pollPopup(window, popuptype, company = null) {
    return new Promise((resolve, reject) => {
        const polling = setInterval(() => {
        if (!window || window.closed || window.closed === undefined) {
          clearInterval(polling);
          reject(new Error('The popup window was closed'));
        }
        try {
          if (popuptype === 'logout'){
            localStorage.removeItem('token');
            localStorage.removeItem('login_type');
          }
          const popupUrlPath = window.location.protocol + '//' + window.location.host + window.location.pathname;
          if (popupUrlPath === this.redirecturl) {
            if (window.location.search) {
              const urlParams = new URLSearchParams(window.location.search);
              const error = urlParams.get('error');
              if (error) {
                // Format and display the error nicely
                this.displayFormattedError(window, error, urlParams.get('error_description'), window.location.href);
                // Clear polling and reject to prevent further processing
                clearInterval(polling);
                reject(new Error(`SSO Error: ${error}`));
              } else {
                resolve(urlParams);
                clearInterval(polling);
                window.close();
              }
            } else {
              reject(new Error('OAuth redirect has occurred but no query parameters were found.'));
            }
          }
        } catch (error) {
            // Ignore DOMException: Blocked a frame with origin from accessing a cross-origin frame.
            // A hack to get around same-origin security policy errors in Internet Explorer.
        }
      }, 500);
    });
  }
  /*
  getDevice() {
    const userAgent = navigator.userAgent || navigator.vendor;
      // Windows Phone must come first because its UA also contains "Android"
      if (/windows phone/i.test(userAgent)) {
          return 'windows';
      }
      if (/android/i.test(userAgent)) {
          return 'android';
      }
      if (/iPad/i.test(userAgent) && !window.MSStream) {
          return 'ipad';
      }
      if (/iPhone/.test(userAgent) && !window.MSStream) {
        return 'iphone';
      }
      return 'browser';
  }
  */
 
  getRedirectUrlPath() {
    const origin = location.origin; // includes protocol + hostname + port (if any)
    let basePath = location.pathname;

    // Ensure basePath ends with '/'
    if (!basePath.endsWith('/')) {
      basePath += '/';
    }

    return origin + basePath + 'ssologin';  
  }

  _stringifyOptions() {
    const options = [];
    for (const optionKey in this.popupOptions) {
      if (!this.isUndefined(this.popupOptions[optionKey])) {
        options.push(`${optionKey}=${this.popupOptions[optionKey]}`);
      }
    }
    return options.join(',');
  }
  isUndefined(value: any) {
    return typeof value === 'undefined';
  }

  displayFormattedError(popupWindow: Window, error: string, errorDescription: string, fullUrl: string) {
    try {
      // Create a nicely formatted error page
      popupWindow.document.open();
      popupWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>SSO Login Error</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              margin: 0;
              padding: 20px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .error-container {
              background: white;
              border-radius: 12px;
              box-shadow: 0 10px 30px rgba(0,0,0,0.3);
              max-width: 500px;
              width: 100%;
              overflow: hidden;
            }
            .error-header {
              background: #dc3545;
              color: white;
              padding: 20px;
              text-align: center;
            }
            .error-header h1 {
              margin: 0;
              font-size: 24px;
              font-weight: 600;
            }
            .error-icon {
              font-size: 48px;
              margin-bottom: 10px;
            }
            .error-body {
              padding: 30px;
            }
            .error-message {
              background: #f8f9fa;
              border-left: 4px solid #dc3545;
              padding: 15px;
              margin: 20px 0;
              border-radius: 4px;
            }
            .error-message strong {
              color: #dc3545;
              display: block;
              margin-bottom: 8px;
            }
            .error-details {
              background: #e9ecef;
              padding: 15px;
              border-radius: 6px;
              margin: 15px 0;
              font-size: 14px;
              color: #6c757d;
            }
            .error-actions {
              text-align: center;
              margin-top: 25px;
            }
            .btn {
              background: #007bff;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 16px;
              margin: 0 10px;
              transition: background-color 0.3s;
            }
            .btn:hover {
              background: #0056b3;
            }
            .btn-secondary {
              background: #6c757d;
            }
            .btn-secondary:hover {
              background: #545b62;
            }
            .troubleshooting {
              margin-top: 20px;
              padding: 15px;
              background: #fff3cd;
              border: 1px solid #ffeaa7;
              border-radius: 6px;
            }
            .troubleshooting h4 {
              margin: 0 0 10px 0;
              color: #856404;
            }
            .troubleshooting ul {
              margin: 0;
              padding-left: 20px;
              color: #856404;
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <div class="error-header">
              <div class="error-icon">🔐</div>
              <h1>SSO Login Error</h1>
            </div>
            <div class="error-body">
              <div class="error-message">
                <strong>Error:</strong>
                ${this.escapeHtml(error)}
              </div>
              ${errorDescription ? `
                <div class="error-message">
                  <strong>Description:</strong>
                  ${this.escapeHtml(errorDescription)}
                </div>
              ` : ''}
              <div class="error-details">
                <strong>URL:</strong> ${this.escapeHtml(fullUrl)}
              </div>
              <div class="troubleshooting">
                <h4>💡 Troubleshooting Tips:</h4>
                <ul>
                  <li>Contact your system administrator to enable SSO for this application</li>
                  <li>Try using email login instead of SSO</li>
                  <li>Clear your browser cache and cookies</li>
                  <li>Restart your browser and try again</li>
                </ul>
              </div>
              <div class="error-actions">
                <button class="btn" onclick="window.close()">Close Window</button>
                <button class="btn btn-secondary" onclick="window.location.reload()">Try Again</button>
              </div>
            </div>
          </div>
        </body>
        </html>
      `);
      popupWindow.document.close();
    } catch (displayError) {
      console.error('Error displaying formatted error:', displayError);
      // Fallback to simple error display
      try {
        popupWindow.document.write(`
          <div style="padding: 20px; font-family: Arial, sans-serif; background: #f8f9fa;">
            <h2 style="color: #dc3545;">SSO Login Error</h2>
            <div style="background: white; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px;">
              <p><strong>Error:</strong> ${this.escapeHtml(error)}</p>
              ${errorDescription ? `<p><strong>Description:</strong> ${this.escapeHtml(errorDescription)}</p>` : ''}
              <p><strong>URL:</strong> ${this.escapeHtml(fullUrl)}</p>
            </div>
            <button onclick="window.close()" style="margin-top: 15px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
              Close Window
            </button>
          </div>
        `);
      } catch (fallbackError) {
        console.error('Even fallback error display failed:', fallbackError);
        // Ultimate fallback
        popupWindow.document.write(error + '<br />Try restarting browser or clear recent history.');
      }
    }
  }

  escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}
