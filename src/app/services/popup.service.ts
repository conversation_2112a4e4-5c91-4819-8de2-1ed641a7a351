import { Injectable, Injector } from '@angular/core';
import { ConfigService } from './config.service';
import { AuthenticationService } from './authentication.service';

@Injectable({
  providedIn: 'root'
})
export class PopupService {
  public application = 'DIGITAL_FORMS';
  public redirecturl = this.getRedirectUrlPath();
  public device = 'browser';
  public popupurl;
  public popupOptions = {
    width: 600,
    height: 600,
    top: window.screenY + ((window.outerHeight - 600) / 2.5),
    left: window.screenX + ((window.outerWidth - 600) / 2)
  };
  constructor(private injector: Injector) { }
  openPopup (popuptype: string, company: string = null): Promise<any> {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';
   if (popuptype === 'login') {
    if (!company) {
      company = localStorage.getItem('domain');
    }
    this.popupurl = `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
   } else if (popuptype === 'logout') {
    const token = localStorage.getItem('token');
    this.popupurl = `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;

   } else {
     console.log('popup type missing');
     return Promise.reject(new Error('Invalid popup type'));
   }
    try {
      const popup = window.open(this.popupurl, '_blank', this._stringifyOptions());
      if (popup && popup.focus) {
        popup.focus();
      }
      if (this.popupurl === 'about:blank') {
        popup.document.body.innerHTML = 'Loading...';
      }
      return this.pollPopup(popup, popuptype, company).then(
        (params: URLSearchParams) => {
            if (popuptype === 'login') {
              const token = params.get('token');
              // Call authentication service to properly store all user details
              const authService = this.injector.get(AuthenticationService);
              const jwtres = { token: token };
              const user = { domain: company };
              authService.storeTokenAndlogIn(jwtres, user);
              // Override login_type to SSO after storeTokenAndlogIn
              localStorage.setItem('login_type', 'SSO');
              window.location.reload();
            } else {
              // For logout, preserve domain but remove other auth data
              const preservedDomain = localStorage.getItem('domain');
              localStorage.removeItem('token');
              localStorage.removeItem('login_type');
              localStorage.removeItem('name');
              localStorage.removeItem('Role');
              localStorage.removeItem('landscape');
              localStorage.removeItem('email');
              localStorage.removeItem('UMP_url');
              // Restore domain for future SSO logins
              if (preservedDomain) {
                localStorage.setItem('domain', preservedDomain);
              }
              window.location.reload();
            }
            return { success: true };
        }
        ).catch(
          error => {
            console.log('SSO Popup Error:', error);
            // Return error to be handled by calling component
            return { success: false, error: error.message };
          }
        );

    } catch (e) {
      console.log('Error opening popup:', e);
      return Promise.reject(new Error('Failed to open popup window'));
    }
  }
  pollPopup(window, popuptype, company = null) {
    return new Promise((resolve, reject) => {
        console.log('Starting popup polling...');
        let pollCount = 0;
        const polling = setInterval(() => {
        pollCount++;
        console.log(`Poll attempt ${pollCount}`);

        if (!window || window.closed || window.closed === undefined) {
          console.log('Popup window was closed by user');
          clearInterval(polling);
          reject(new Error('The popup window was closed'));
        }
        try {
          const popupUrlPath = window.location.protocol + '//' + window.location.host + window.location.pathname;
          console.log('Popup URL:', popupUrlPath);
          console.log('Expected redirect URL:', this.redirecturl);

          if (popupUrlPath === this.redirecturl) {
            console.log('Redirect URL matched!');
            if (window.location.search) {
              console.log('Query params found:', window.location.search);
              const urlParams = new URLSearchParams(window.location.search);
              const error = urlParams.get('error');
              if (error) {
                // Close popup and return error info to main app
                clearInterval(polling);
                window.close();
                reject(new Error(`SSO Error: ${error} - ${urlParams.get('error_description') || ''}`));
              } else {
                // Check if we have expected parameters for success
                const token = urlParams.get('token');
                console.log('Token found:', token);
                if (token || popuptype === 'logout') {
                  // We have a token for login, or this is logout (which may not have token)
                  console.log('Success! Resolving with params');
                  resolve(urlParams);
                  clearInterval(polling);
                  window.close();
                } else {
                  // We have query params but no token - this might be an intermediate redirect
                  // Continue polling instead of rejecting immediately
                  console.log('Redirect detected with query params but no token, continuing to poll...');
                }
              }
            } else {
              // No query parameters - this could be an intermediate redirect, continue polling
              console.log('Redirect detected without query params, continuing to poll...');
            }
          }
        } catch (error) {
            // Ignore DOMException: Blocked a frame with origin from accessing a cross-origin frame.
            // A hack to get around same-origin security policy errors in Internet Explorer.
            console.log('Cross-origin access blocked (expected)');
        }

        // Add timeout to prevent infinite polling
        if (pollCount > 120) { // 60 seconds at 500ms intervals
          console.log('Popup polling timeout reached');
          clearInterval(polling);
          window.close();
          reject(new Error('SSO authentication timeout - please try again'));
        }
      }, 500);
    });
  }
  /*
  getDevice() {
    const userAgent = navigator.userAgent || navigator.vendor;
      // Windows Phone must come first because its UA also contains "Android"
      if (/windows phone/i.test(userAgent)) {
          return 'windows';
      }
      if (/android/i.test(userAgent)) {
          return 'android';
      }
      if (/iPad/i.test(userAgent) && !window.MSStream) {
          return 'ipad';
      }
      if (/iPhone/.test(userAgent) && !window.MSStream) {
        return 'iphone';
      }
      return 'browser';
  }
  */
 
  getRedirectUrlPath() {
    const origin = location.origin; // includes protocol + hostname + port (if any)
    const pathname = location.pathname; // includes the base href path like /app

    // Extract base path (everything except the current route)
    const basePath = pathname.substring(0, pathname.lastIndexOf('/')) || '';

    // Construct full redirect URL respecting deployment path
    return origin + basePath + '/login';
  }

  getLoginUrl(popuptype: string, company: string = null): string {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';

    if (popuptype === 'login') {
      if (!company) {
        company = localStorage.getItem('domain');
      }
      return `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
    } else if (popuptype === 'logout') {
      const token = localStorage.getItem('token');
      return `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;
    } else {
      return 'Invalid popup type';
    }
  }

  _stringifyOptions() {
    const options = [];
    for (const optionKey in this.popupOptions) {
      if (!this.isUndefined(this.popupOptions[optionKey])) {
        options.push(`${optionKey}=${this.popupOptions[optionKey]}`);
      }
    }
    return options.join(',');
  }
  isUndefined(value: any) {
    return typeof value === 'undefined';
  }


}
