import { Injectable, Injector } from '@angular/core';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class PopupService {
  public application = 'DIGITAL_FORMS';
  public redirecturl = this.getRedirectUrlPath();
  public device = 'browser';
  public popupurl;
  public popupOptions = {
    width: 600,
    height: 600,
    top: window.screenY + ((window.outerHeight - 600) / 2.5),
    left: window.screenX + ((window.outerWidth - 600) / 2)
  };
  constructor(private injector: Injector) { }
  openPopup (popuptype: string, company: string = null) {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';
   if (popuptype === 'login') {
    if (!company) {
      company = localStorage.getItem('domain');
    }
    this.popupurl = `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
   } else if (popuptype === 'logout') {
    const token = localStorage.getItem('token');
    this.popupurl = `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;

   } else {
     console.log('popup type missing');
   }
    try {
      const popup = window.open(this.popupurl, '_blank', this._stringifyOptions());
      if (popup && popup.focus) {
        popup.focus();
      }
      if (this.popupurl === 'about:blank') {
        popup.document.body.innerHTML = 'Loading...';
      }
      this.pollPopup(popup, popuptype, company).then(
        (params: URLSearchParams) => {
            if (popuptype === 'login') {
              const token = params.get('token');
              localStorage.setItem('token', token);
              localStorage.setItem('login_type', 'SSO');
              // Save the domain used for SSO login
              if (company) {
                localStorage.setItem('domain', company);
              }
              window.location.reload();
            } else {
              localStorage.removeItem('token');
              localStorage.removeItem('login_type');
              window.location.reload();
            }
        }
        ).catch(
          error => {
            console.error('SSO Popup Error:', error);
            // Error is already displayed in the popup window by displayFormattedError
            // Just log it here for debugging purposes
          }
        );

    } catch (e) {
      console.log('Error opening popup:', e);
      // return throwError(new Error('OAuth popup error occurred'))
    }
  }
  pollPopup(window, popuptype, company = null) {
    return new Promise((resolve, reject) => {
        const polling = setInterval(() => {
        if (!window || window.closed || window.closed === undefined) {
          clearInterval(polling);
          reject(new Error('The popup window was closed'));
        }
        try {
          if (popuptype === 'logout'){
            localStorage.removeItem('token');
            localStorage.removeItem('login_type');
          }
          const popupUrlPath = window.location.protocol + '//' + window.location.host + window.location.pathname;
          if (popupUrlPath === this.redirecturl) {
            if (window.location.search) {
              const urlParams = new URLSearchParams(window.location.search);
              const error = urlParams.get('error');
              const errorDescription = urlParams.get('error_description') || urlParams.get('message') || '';

              // Debug logging
              console.log('SSO Popup URL params:', {
                fullUrl: window.location.href,
                search: window.location.search,
                error: error,
                errorDescription: errorDescription,
                allParams: Object.fromEntries(urlParams.entries())
              });

              // Check if there's actually an error (not null, not empty string)
              if (error && error.trim() !== '') {
                console.error('SSO Error detected:', error, errorDescription);
                try {
                  // Display formatted error message
                  this.displayFormattedError(window, error, errorDescription, window.location.href);
                } catch (displayError) {
                  console.error('Failed to display formatted error, falling back to simple display:', displayError);
                  // Fallback to simple error display
                  window.document.body.innerHTML = `
                    <div style="padding: 20px; font-family: Arial, sans-serif;">
                      <h2 style="color: red;">SSO Login Error</h2>
                      <p><strong>Error:</strong> ${this.escapeHtml(error)}</p>
                      ${errorDescription ? `<p><strong>Description:</strong> ${this.escapeHtml(errorDescription)}</p>` : ''}
                      <button onclick="window.close()" style="padding: 10px 20px; margin-top: 20px;">Close Window</button>
                    </div>
                  `;
                }
                clearInterval(polling);
                // Don't close the popup immediately so user can see the error
                // User will need to close it manually or click a close button
                reject(new Error(`SSO Error: ${error}${errorDescription ? ' - ' + errorDescription : ''}`));
              } else {
                // Success case - close popup and resolve
                clearInterval(polling);
                window.close();
                resolve(urlParams);
              }
            } else {
              // No query parameters found - this might be a successful redirect without parameters
              // or an error case. Let's handle it gracefully.
              console.warn('OAuth redirect occurred but no query parameters found');
              clearInterval(polling);
              window.close();
              resolve(new URLSearchParams()); // Return empty params for success case
            }
          }
        } catch (error) {
            // Ignore DOMException: Blocked a frame with origin from accessing a cross-origin frame.
            // A hack to get around same-origin security policy errors in Internet Explorer.
        }
      }, 500);
    });
  }
  /*
  getDevice() {
    const userAgent = navigator.userAgent || navigator.vendor;
      // Windows Phone must come first because its UA also contains "Android"
      if (/windows phone/i.test(userAgent)) {
          return 'windows';
      }
      if (/android/i.test(userAgent)) {
          return 'android';
      }
      if (/iPad/i.test(userAgent) && !window.MSStream) {
          return 'ipad';
      }
      if (/iPhone/.test(userAgent) && !window.MSStream) {
        return 'iphone';
      }
      return 'browser';
  }
  */
 
  getRedirectUrlPath() {
    const origin = location.origin; // includes protocol + hostname + port (if any)
    let basePath = location.pathname;

    // Ensure basePath ends with '/'
    if (!basePath.endsWith('/')) {
      basePath += '/';
    }

    return origin + basePath + 'ssologin';  
  }

  _stringifyOptions() {
    const options = [];
    for (const optionKey in this.popupOptions) {
      if (!this.isUndefined(this.popupOptions[optionKey])) {
        options.push(`${optionKey}=${this.popupOptions[optionKey]}`);
      }
    }
    return options.join(',');
  }
  isUndefined(value) {
    return typeof value === 'undefined';
  }

  displayFormattedError(popupWindow: Window, error: string, errorDescription: string, fullUrl: string) {
    const errorHtml = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SSO Login Error</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
          }
          .error-container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }
          .error-header {
            background: #dc3545;
            color: white;
            padding: 20px;
            text-align: center;
          }
          .error-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
          }
          .error-content {
            padding: 30px;
          }
          .error-message {
            background: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
          }
          .error-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #dc3545;
            font-size: 16px;
          }
          .error-description {
            margin-top: 10px;
            color: #666;
            line-height: 1.5;
          }
          .error-details {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
          }
          .error-details summary {
            cursor: pointer;
            font-weight: 600;
            color: #495057;
            outline: none;
          }
          .error-details summary:hover {
            color: #007bff;
          }
          .url-display {
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #6c757d;
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
          }
          .action-buttons {
            text-align: center;
            margin-top: 30px;
          }
          .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
          }
          .btn-primary {
            background: #007bff;
            color: white;
          }
          .btn-primary:hover {
            background: #0056b3;
          }
          .btn-secondary {
            background: #6c757d;
            color: white;
          }
          .btn-secondary:hover {
            background: #545b62;
          }
          .help-text {
            text-align: center;
            color: #6c757d;
            font-size: 14px;
            margin-top: 20px;
            line-height: 1.5;
          }
        </style>
      </head>
      <body>
        <div class="error-container">
          <div class="error-header">
            <h1>🔐 SSO Login Error</h1>
          </div>
          <div class="error-content">
            <div class="error-message">
              <div class="error-code">Error: ${this.escapeHtml(error)}</div>
              ${errorDescription ? `<div class="error-description">${this.escapeHtml(errorDescription)}</div>` : ''}
            </div>

            <div class="error-details">
              <details>
                <summary>Technical Details</summary>
                <div class="url-display">
                  <strong>Full URL:</strong><br>
                  ${this.escapeHtml(fullUrl)}
                </div>
              </details>
            </div>

            <div class="action-buttons">
              <button class="btn btn-primary" onclick="window.close()">Close Window</button>
              <button class="btn btn-secondary" onclick="window.location.reload()">Retry</button>
            </div>

            <div class="help-text">
              <strong>Troubleshooting Tips:</strong><br>
              • To avoid browser cache issues, please restart your browser and/or clear recent history<br>
              • Contact your system administrator if the problem persists<br>
              • Make sure your domain and SSO configuration are correct
            </div>
          </div>
        </div>

        <script>
          // Auto-focus the close button for keyboard accessibility
          document.querySelector('.btn-primary').focus();

          // Allow closing with Escape key
          document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
              window.close();
            }
          });
        </script>
      </body>
      </html>
    `;

    // Clear the document and write the formatted error
    popupWindow.document.open();
    popupWindow.document.write(errorHtml);
    popupWindow.document.close();
  }

  escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}
