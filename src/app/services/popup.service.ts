import { Injectable, Injector } from '@angular/core';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class PopupService {
  public application = 'DIGITAL_FORMS';
  public redirecturl = this.getRedirectUrlPath();
  public device = 'browser';
  public popupurl;
  public popupOptions = {
    width: 600,
    height: 600,
    top: window.screenY + ((window.outerHeight - 600) / 2.5),
    left: window.screenX + ((window.outerWidth - 600) / 2)
  };
  constructor(private injector: Injector) { }
  openPopup (popuptype: string, company: string = null) {
    const configService = this.injector.get(ConfigService);
    let base_popupurl = configService.apiBaseUrl + '/UMP/sso/saml';
   if (popuptype === 'login') {
    if (!company) {
      company = localStorage.getItem('domain');
    }
    this.popupurl = `${base_popupurl}/dologin?company=${company}&application=${this.application}&device=${this.device}&redirect=${this.redirecturl}`;
   } else if (popuptype === 'logout') {
    const token = localStorage.getItem('token');
    this.popupurl = `${base_popupurl}/dologout?token=${token}&redirect=${this.redirecturl}`;

   } else {
     console.log('popup type missing');
   }
    try {
      const popup = window.open(this.popupurl, '_blank', this._stringifyOptions());
      if (popup && popup.focus) {
        popup.focus();
      }
      if (this.popupurl === 'about:blank') {
        popup.document.body.innerHTML = 'Loading...';
      }
      this.pollPopup(popup, popuptype, company).then(
        (params: URLSearchParams) => {
            if (popuptype === 'login') {
              const token = params.get('token');
              localStorage.setItem('token', token);
              localStorage.setItem('login_type', 'SSO');
              // Save the domain used for SSO login
              if (company) {
                localStorage.setItem('domain', company);
              }
              window.location.reload();
            } else {
              localStorage.removeItem('token');
              localStorage.removeItem('login_type');
              window.location.reload();
            }
        }
        ).catch(
          error => {
            console.error('SSO Popup Error:', error);
            // Error is already displayed in the popup window by displayFormattedError
            // Just log it here for debugging purposes
          }
        );

    } catch (e) {
      console.log('Error opening popup:', e);
      // return throwError(new Error('OAuth popup error occurred'))
    }
  }
  pollPopup(window, popuptype, company = null) {
    return new Promise((resolve, reject) => {
        const polling = setInterval(() => {
        if (!window || window.closed || window.closed === undefined) {
          clearInterval(polling);
          reject(new Error('The popup window was closed'));
        }
        try {
          if (popuptype === 'logout'){
            localStorage.removeItem('token');
            localStorage.removeItem('login_type');
          }
          const popupUrlPath = window.location.protocol + '//' + window.location.host + window.location.pathname;
          if (popupUrlPath === this.redirecturl) {
            if (window.location.search) {
              const urlParams = new URLSearchParams(window.location.search);
              const error = urlParams.get('error');
              const errorDescription = urlParams.get('error_description') || urlParams.get('message') || '';

              // Debug logging
              console.log('SSO Popup URL params:', {
                fullUrl: window.location.href,
                search: window.location.search,
                error: error,
                errorDescription: errorDescription,
                allParams: Object.fromEntries(urlParams.entries())
              });

              // Check if there's actually an error (not null, not empty string)
              if (error && error.trim() !== '') {
                console.error('SSO Error detected:', error, errorDescription);
                try {
                  // Display formatted error message
                  this.displayFormattedError(window, error, errorDescription, window.location.href);
                } catch (displayError) {
                  console.error('Failed to display formatted error, falling back to simple display:', displayError);
                  // Fallback to simple error display
                  window.document.body.innerHTML = `
                    <div style="padding: 20px; font-family: Arial, sans-serif;">
                      <h2 style="color: red;">SSO Login Error</h2>
                      <p><strong>Error:</strong> ${this.escapeHtml(error)}</p>
                      ${errorDescription ? `<p><strong>Description:</strong> ${this.escapeHtml(errorDescription)}</p>` : ''}
                      <button onclick="window.close()" style="padding: 10px 20px; margin-top: 20px;">Close Window</button>
                    </div>
                  `;
                }
                clearInterval(polling);
                // Don't close the popup immediately so user can see the error
                // User will need to close it manually or click a close button
                reject(new Error(`SSO Error: ${error}${errorDescription ? ' - ' + errorDescription : ''}`));
              } else {
                // Success case - close popup and resolve
                console.log('SSO Success - no error detected, closing popup');
                clearInterval(polling);
                window.close();
                resolve(urlParams);
              }
            } else {
              // No query parameters found - this might be a successful redirect without parameters
              // or an error case. Let's handle it gracefully.
              console.warn('OAuth redirect occurred but no query parameters found');
              clearInterval(polling);
              window.close();
              resolve(new URLSearchParams()); // Return empty params for success case
            }
          }
        } catch (error) {
            // Ignore DOMException: Blocked a frame with origin from accessing a cross-origin frame.
            // A hack to get around same-origin security policy errors in Internet Explorer.
        }
      }, 500);
    });
  }
  /*
  getDevice() {
    const userAgent = navigator.userAgent || navigator.vendor;
      // Windows Phone must come first because its UA also contains "Android"
      if (/windows phone/i.test(userAgent)) {
          return 'windows';
      }
      if (/android/i.test(userAgent)) {
          return 'android';
      }
      if (/iPad/i.test(userAgent) && !window.MSStream) {
          return 'ipad';
      }
      if (/iPhone/.test(userAgent) && !window.MSStream) {
        return 'iphone';
      }
      return 'browser';
  }
  */
 
  getRedirectUrlPath() {
    const origin = location.origin; // includes protocol + hostname + port (if any)
    let basePath = location.pathname;

    // Ensure basePath ends with '/'
    if (!basePath.endsWith('/')) {
      basePath += '/';
    }

    return origin + basePath + 'ssologin';  
  }

  _stringifyOptions() {
    const options = [];
    for (const optionKey in this.popupOptions) {
      if (!this.isUndefined(this.popupOptions[optionKey])) {
        options.push(`${optionKey}=${this.popupOptions[optionKey]}`);
      }
    }
    return options.join(',');
  }
  isUndefined(value) {
    return typeof value === 'undefined';
  }

  displayFormattedError(popupWindow: Window, error: string, errorDescription: string, fullUrl: string) {
    try {
      // Use a simpler, more reliable approach
      popupWindow.document.body.innerHTML = `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto;">
          <div style="background: #dc3545; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">🔐 SSO Login Error</h1>
          </div>

          <div style="background: white; border: 1px solid #dc3545; border-top: none; border-radius: 0 0 8px 8px; padding: 30px;">
            <div style="background: #f8f9fa; border-left: 4px solid #dc3545; padding: 15px; margin: 20px 0; border-radius: 4px;">
              <div style="font-family: 'Courier New', monospace; font-weight: bold; color: #dc3545; font-size: 16px; margin-bottom: 10px;">
                Error: ${this.escapeHtml(error)}
              </div>
              ${errorDescription ? `<div style="color: #666; line-height: 1.5;">${this.escapeHtml(errorDescription)}</div>` : ''}
            </div>

            <details style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 4px; border: 1px solid #e9ecef;">
              <summary style="cursor: pointer; font-weight: 600; color: #495057;">Technical Details</summary>
              <div style="margin-top: 10px; word-break: break-all; font-family: 'Courier New', monospace; font-size: 12px; color: #6c757d; background: #f1f3f4; padding: 10px; border-radius: 4px;">
                <strong>Full URL:</strong><br>
                ${this.escapeHtml(fullUrl)}
              </div>
            </details>

            <div style="text-align: center; margin: 30px 0;">
              <button onclick="window.close()" style="background: #007bff; color: white; border: none; padding: 12px 24px; margin: 0 10px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 600;">
                Close Window
              </button>
              <button onclick="window.location.reload()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; margin: 0 10px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 600;">
                Retry
              </button>
            </div>

            <div style="text-align: center; color: #6c757d; font-size: 14px; margin-top: 20px; line-height: 1.5;">
              <strong>Troubleshooting Tips:</strong><br>
              • To avoid browser cache issues, please restart your browser and/or clear recent history<br>
              • Contact your system administrator if the problem persists<br>
              • Make sure your domain and SSO configuration are correct
            </div>
          </div>
        </div>
      `;

      // Add keyboard event listener
      popupWindow.document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
          popupWindow.close();
        }
      });

    } catch (displayError) {
      console.error('Error displaying formatted error:', displayError);
      // Ultimate fallback - just use alert or simple text
      popupWindow.document.body.innerHTML = `
        <div style="padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: red;">SSO Login Error</h2>
          <p><strong>Error:</strong> ${this.escapeHtml(error)}</p>
          ${errorDescription ? `<p><strong>Description:</strong> ${this.escapeHtml(errorDescription)}</p>` : ''}
          <p><strong>URL:</strong> ${this.escapeHtml(fullUrl)}</p>
          <button onclick="window.close()" style="padding: 10px 20px; margin-top: 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Close Window
          </button>
        </div>
      `;
    }
  }

  escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}
