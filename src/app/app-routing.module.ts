import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { AppLayoutComponent } from "./layout/app.layout.component";
import { LoginComponent } from './auth/login/login.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
// import { PageNotFoundComponent } from './shared/components/page-not-found/page-not-found.component';
import { anonymousGuard } from './auth/guards/anonymous.guard';
import { authGuard, authGuardChild } from './auth/guards/auth.guard';
import { UsersComponent } from './components/users/users/users.component';
import { FormPreviewComponent } from './components/Forms/form-preview/form-preview.component';
import { PublicSharedFormDataComponent } from './components/Forms/public-shared-form-data/public-shared-form-data.component';
import { roleGuard } from './auth/guards/role.guard';
import { PersonalizationComponent } from './components/personalization/personalization.component';
import { ResetPasswordComponent } from './auth/reset-password/reset-password.component';
import { LayoutsAndTemplatesComponent } from './components/Forms/layouts-and-templates/layouts-and-templates.component';

const routes: Routes = [
    { path: '', redirectTo: 'login', pathMatch: 'full' },
    {
        path: '', 
        component: AppLayoutComponent,
        canActivateChild: [authGuardChild],
        children: [
            { 
                path: 'home', 
                component: DashboardComponent 
            },
            { 
                path: 'users',
                component: UsersComponent,
                data: { role: 'Admin' },
                canActivate: [roleGuard]
            },
            { 
                path: 'personalization',
                component: PersonalizationComponent,
            },
            {
                path: 'forms',
                loadChildren: () => import('./components/Forms/forms.module').then(mod => mod.FormsModule)
            },
            // { 
            //     path: 'templates',
            //     component: LayoutsAndTemplatesComponent 
            // },
            {
                path: 'flows',
                loadChildren: () => import('./components/rete/rete.module').then(mod=>mod.ReteEditorModule)
            },
            {
                path: 'reports',
                loadChildren: () => import('./components/reports/reports.module').then(mod => mod.ReportsModule)
            },
            {
                path: 'setting',
                loadChildren: () => import('./components/settings/settings.module').then(mod=>mod.SettingsModule)
            }
        ]
    },
    {
        path: 'login',
        component: LoginComponent,
        canActivate: [anonymousGuard],
    },
    {
        path: 'resetpassword',
        component: ResetPasswordComponent,
        canActivate: [anonymousGuard],
    },
    { 
        path: 'preview/:data', 
        component: FormPreviewComponent,
        data: { role: ['Admin', 'Developer'] },
        canActivate: [authGuard,roleGuard]
    },
    {
        path: 'public-shared-forms/:data',
        component: PublicSharedFormDataComponent,
        data: { role: ['Admin', 'Developer'] },
        canActivate: [authGuard,roleGuard]
    },
    
];

 @NgModule({
    imports: [
        RouterModule.forRoot(routes, { scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled', onSameUrlNavigation: 'reload' })
    ],
    exports: [RouterModule],
    // providers: [
    //     CanDeactivateGuard
    // ]
})
export class AppRoutingModule {
}
