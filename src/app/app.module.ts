import { APP_INITIALIZER, NgModule } from '@angular/core';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { AppLayoutModule } from './layout/app.layout.module';
import { LoginComponent } from './auth/login/login.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { SharedModule } from './shared/shared.module';
import { ConfigService } from './services/config.service';
import { MonacoEditorModule, NgxMonacoEditorConfig } from 'ngx-monaco-editor';
import jsonataMode from '../app/components/rete/nodes/jsonata-editor/jsonataMode';
import { UsersComponent } from './components/users/users/users.component';

import './custom-components/smart-data/NewSmartData';
import './custom-components/autocomplete/AutoCompleteField';
import './custom-components/barcode/barcode';
import './custom-components/smart-button/SmartButton';
import './custom-components/smart-file/smart-file';
import './custom-components/smart-table/smart-table';
import './custom-components/smart-select/smart-select';
import './custom-components/select-barcode/SelectBarcode';
import './custom-components/smart-id/smart-id';
import './custom-components/nested-component/nested-component';
import './custom-components/smart-signature/Signature';
import './custom-components/Location/location';
import './custom-components/column/Column';


import { FormioModule } from '@formio/angular';
import { httpInterceptorProviders } from './auth';
import { PersonalizationComponent } from './components/personalization/personalization.component';
import { ResetPasswordComponent } from './auth/reset-password/reset-password.component';

const monacoConfig: NgxMonacoEditorConfig = {
   onMonacoLoad: jsonataMode
};
@NgModule({
    declarations: [
        AppComponent, LoginComponent,DashboardComponent, UsersComponent, PersonalizationComponent, ResetPasswordComponent, 
    ],
    imports: [
        AppRoutingModule,
        AppLayoutModule,
        SharedModule,
        FormioModule,
        // QueryBuilderModule,
        // NgxAngularQueryBuilderModule,
        MonacoEditorModule.forRoot(monacoConfig),
 
    ],
    providers: [
        {
          provide: APP_INITIALIZER,
          multi: true,
          deps: [ConfigService],
          useFactory: (appConfigService: ConfigService) => {
            return async () => {
              return await appConfigService.loadAppConfig();
              };
          }
        },
        // { provide: DateAdapter, useClass: MyDateAdapter },
        httpInterceptorProviders
      ],
    bootstrap: [AppComponent]
})
export class AppModule { }
