import { OnInit } from '@angular/core';
import { Component } from '@angular/core';
import { LayoutService } from './service/app.layout.service';
import { PopupService } from '../services/popup.service';
import { AuthenticationService } from '../services/authentication.service';
import { ConfigService } from '../services/config.service';
import { SettingsService } from '../services/settings.service';
import { FormsService } from '../services/forms.service';
import { DomSanitizer } from '@angular/platform-browser';
import { DialogService } from 'primeng/dynamicdialog';
import { AboutComponent } from '../components/settings/about/about.component';

@Component({
    selector: 'app-menu',
    templateUrl: './app.menu.component.html',
    providers: [DialogService]
})
export class AppMenuComponent implements OnInit {
    externalapps = [];
    role: string;
    model: any[] = [];
    logintype: string;
    companysettings: any;
    base64comapnyimage: any;
    profileURL: any
    firstName: string;
    lastName: string;

    constructor(public layoutService: LayoutService, 
        private popupservice: PopupService, 
        private authservice: AuthenticationService,
        private configService: ConfigService,
        private settingservice: SettingsService,
        private formsService: FormsService,
        private sanitizer: DomSanitizer,
        public dialogService: DialogService,
    ) { }

    ngOnInit() {
        const usr = this.authservice.decode();
        this.role = usr.ROLE;
        this.firstName = usr.FNAME;
        this.lastName = usr.LNAME;
        if (this.role.toLowerCase() === 'admin') {
            this.model = [
                {
                    items: [
                        { label: 'Home', icon: 'pi pi-fw pi-home', routerLink: ['/home']},
                        { label: 'Forms', icon: 'pi pi-fw pi-id-card', routerLink: ['/forms']},
                        //{ label: 'Templates', icon: 'pi pi-fw pi-credit-card', routerLink: ['/templates']}, // add template for other roles also
                        { label: 'Users', icon: 'pi pi-fw pi-users', routerLink: ['/users'] },
                        { label: 'Reports', icon: 'pi pi-fw pi-chart-bar', routerLink: ['/reports'] },
                        { label: 'Personalization', icon: 'fa-solid fa-wand-magic-sparkles', routerLink: ['/personalization'] },
                        // { label: 'Flows', icon: 'pi pi-fw pi-qrcode', routerLink: ['/flows'] },
                        { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/setting/general'],
                            items: [
                                {
                                    label: 'General',
                                    icon: 'pi pi-fw pi-globe',
                                    routerLink: ['/setting/general']
                                },
                                {
                                    label: 'Mail Templates',
                                    icon: 'pi pi-fw pi-envelope',
                                    routerLink: ['/setting/email-template']
                                },
                                {
                                    label: 'PDF',
                                    icon: 'pi pi-fw pi-file-pdf',
                                    routerLink: ['/setting/pdf']
                                },
                                {
                                    label: 'Customization',
                                    icon: 'pi pi-fw pi-sliders-h',
                                    routerLink: ['/setting/customization']
                                },
                                {
                                    label: 'Communication',
                                    icon: 'pi pi-fw pi-comments',
                                    routerLink: ['/setting/communication']
                                },
                                {
                                    label: 'Dashboard',
                                    icon: 'pi pi-fw pi-desktop',
                                    routerLink: ['/setting/dashboard']
                                },
                                {
                                    label: 'Developer',
                                    icon: 'pi pi-fw pi-user-edit',
                                    routerLink: ['/setting/developer']
                                },
                                // {
                                //     label: 'About',
                                //     icon: 'pi pi-fw pi-exclamation-circle',
                                //     routerLink: ['/setting/about']
                                // }
                            ]
                        },
                        { label: 'Logout', icon: 'pi pi-fw pi-sign-out', command: () => { this.onLogout();} }
                    ]
                }
            ];
        } else if (this.role.toLowerCase() === 'manager') {
            this.model = [
                {
                    items: [
                        { label: 'Home', icon: 'pi pi-fw pi-home', routerLink: ['/home']},
                        { label: 'Reports', icon: 'pi pi-fw pi-check-circle', routerLink: ['/reports'] },
                        { label: 'Personalization', icon: 'fa-solid fa-wand-magic-sparkles', routerLink: ['/personalization'] },
                        { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/setting/email-template'],
                            items: [
                                {
                                    label: 'Dashboard',
                                    icon: 'pi pi-fw pi-desktop',
                                    routerLink: ['/setting/dashboard']
                                }
                            ]
                        },
                        { label: 'Logout', icon: 'pi pi-fw pi-sign-out', command: () => { this.onLogout();} },
                    ]
                }
            ];
        } else if (this.role.toLowerCase() === 'developer' && usr?.serverType.toLowerCase() !== 'production') {
            this.model = [
                {
                    items: [
                        { label: 'Home', icon: 'pi pi-fw pi-home', routerLink: ['/home']},
                        { label: 'Forms', icon: 'pi pi-fw pi-id-card', routerLink: ['/forms']},
                        //{ label: 'Templates', icon: 'pi pi-fw pi-credit-card', routerLink: ['/templates']},
                        { label: 'Reports', icon: 'pi pi-fw pi-check-circle', routerLink: ['/reports'] },
                        { label: 'Personalization', icon: 'fa-solid fa-wand-magic-sparkles', routerLink: ['/personalization'] },
                        { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/setting/email-template'],
                            items: [
                                {
                                    label: 'Mail Templates',
                                    icon: 'pi pi-fw pi-envelope',
                                    routerLink: ['/setting/email-template']
                                },
                                {
                                    label: 'PDF',
                                    icon: 'pi pi-fw pi-file-pdf',
                                    routerLink: ['/setting/pdf']
                                },
                                {
                                    label: 'Communication',
                                    icon: 'pi pi-fw pi-comments',
                                    routerLink: ['/setting/communication']
                                },
                                {
                                    label: 'Dashboard',
                                    icon: 'pi pi-fw pi-desktop',
                                    routerLink: ['/setting/dashboard']
                                },
                                {
                                    label: 'Developer',
                                    icon: 'pi pi-fw pi-user-edit',
                                    routerLink: ['/setting/developer']
                                },
                            ]
                        },
                        { label: 'Logout', icon: 'pi pi-fw pi-sign-out', command: () => { this.onLogout();} }
                    ]
                }
            ];
        } else if (this.role.toLowerCase() === 'developer' && usr?.serverType.toLowerCase() === 'production') {
            this.model = [
                {
                    items: [
                        { label: 'Home', icon: 'pi pi-fw pi-home', routerLink: ['/home']},
                        { label: 'Forms', icon: 'pi pi-fw pi-id-card', routerLink: ['/forms']},
                        //{ label: 'Templates', icon: 'pi pi-fw pi-credit-card', routerLink: ['/templates']},
                        { label: 'Personalization', icon: 'fa-solid fa-wand-magic-sparkles', routerLink: ['/personalization'] },
                        { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/setting/email-template'],
                            items: [
                                {
                                    label: 'Mail Templates',
                                    icon: 'pi pi-fw pi-envelope',
                                    routerLink: ['/setting/email-template']
                                },
                                {
                                    label: 'PDF',
                                    icon: 'pi pi-fw pi-file-pdf',
                                    routerLink: ['/setting/pdf']
                                },
                                {
                                    label: 'Communication',
                                    icon: 'pi pi-fw pi-comments',
                                    routerLink: ['/setting/communication']
                                },
                                {
                                    label: 'Developer',
                                    icon: 'pi pi-fw pi-user-edit',
                                    routerLink: ['/setting/developer']
                                },
                            ]
                        },
                        { label: 'Logout', icon: 'pi pi-fw pi-sign-out', command: () => { this.onLogout();} }
                    ]
                }
            ];
        }
        
        if(this.configService.app && this.configService.app.length > 0) {
            let cols = 1, rows = 1;
            this.configService.app.forEach(element => {
              let appobj = {
                title:element.name,
                icon: element.icon,
                iconcolor: element.icon || '#000',
                desc: element.description || 'App Description',
                cols: cols,
                rows: rows,
                url: element.url,
                target: element.target
              };
              this.externalapps.push(appobj);
            });
      
          }
          
          this.settingservice.getcompanysettings().subscribe((res: any) => 
            {
                if (res.status.toLowerCase() === 'success') {
                    localStorage.setItem('companysettings', JSON.stringify(res));
                    this.useComapnySettingsData(res);
                }
                this.layoutService.getProfileSetting();
            });
        this.getProfileSetting();
    }

    useComapnySettingsData(res){
        this.companysettings = res.company;
        this.formsService.companySettings.next(res);
        this.base64comapnyimage = res.company.attachmentId;
        if (!res.dashboard) {  // if superset is not configured do not show dashboard in settings
            for (let i=0; i < this.model[0]?.items.length; i++) {
                if (this.model[0]?.items[i]?.label === 'Settings') {
                    const arr = this.model[0]?.items[i]?.items?.filter(item => item?.label !== 'Dashboard');
                    this.model[0].items[i].items = arr;
                }
            }
        }
        
    }

    transform() {
        return this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpeg;base64,' + this.base64comapnyimage);
    }

    onLogout() {
        if (this.logintype === 'SSO') {
            this.layoutService.destroyObservable();
            this.popupservice.openPopup('logout');
        } else {
            this.layoutService.destroyObservable();
            this.authservice.removeTokenAndlogOut();
        }
    }

    gotoAboutPage() {
        const ref = this.dialogService.open(AboutComponent, {
            header: 'About',
            width: '50%'
        });
        ref.onClose.subscribe();
    }

    convertNameToAvatar(str: string) {
        return str.charAt(0).toUpperCase();
    }

    getProfileSetting() {
        this.layoutService.getProfileData().subscribe((res: any)=>{ 
            if (res?.userAvatar) {
                this.profileURL = res.userAvatar;
            }
        });
        // this.settingservice.getPersonalization().subscribe(response => {
        //     if (response.status.toLowerCase() === 'success') {
        //         if (response.userAvatar) {
        //             this.profileURL = response.userAvatar;
        //         }
        //     } else {
        //       console.log(response?.error)
        //     }
        // });
    }
}
