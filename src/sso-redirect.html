<!DOCTYPE html>
<html>
<head>
    <title>SSO Redirect Handler</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h3>Processing SSO Authentication...</h3>
        <p>Please wait while we complete your login.</p>
    </div>

    <script>
        // Handle SSO redirect and communicate result back to main window
        function handleSSORedirect() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const error = urlParams.get('error');
                const token = urlParams.get('token');
                
                let result;
                if (error) {
                    result = {
                        success: false,
                        error: `SSO Error: ${error} - ${urlParams.get('error_description') || ''}`
                    };
                } else if (token) {
                    result = {
                        success: true,
                        token: token
                    };
                } else {
                    result = {
                        success: false,
                        error: 'No token or error received from SSO provider'
                    };
                }
                
                // Store result in localStorage for main window to pick up
                localStorage.setItem('sso_result', JSON.stringify(result));
                
                // Try to close this tab/window
                setTimeout(() => {
                    window.close();
                    // If window.close() doesn't work, show a message
                    if (!window.closed) {
                        document.body.innerHTML = `
                            <div class="container">
                                <h3>Authentication ${result.success ? 'Successful' : 'Failed'}</h3>
                                <p>${result.success ? 'You can now close this tab and return to the main application.' : result.error}</p>
                                <button onclick="window.close()">Close Tab</button>
                            </div>
                        `;
                    }
                }, 1000);
                
            } catch (e) {
                console.error('Error handling SSO redirect:', e);
                localStorage.setItem('sso_result', JSON.stringify({
                    success: false,
                    error: 'Error processing SSO response'
                }));
                
                setTimeout(() => {
                    window.close();
                }, 1000);
            }
        }
        
        // Run when page loads
        window.addEventListener('load', handleSSORedirect);
    </script>
</body>
</html>
